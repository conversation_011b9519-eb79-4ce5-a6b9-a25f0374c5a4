//
//  ShareViewController.swift
//  YoutubeUploaderShareExtension
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 22/05/25.
//

import Cocoa
import UniformTypeIdentifiers
import AVFoundation

class ShareViewController: NSViewController {
    private var containerView: NSView!
    private var progressIndicator: NSProgressIndicator!
    private var statusLabel: NSTextField!
    
    private let mainAppBundleIdentifier = "com.codecraft.YouTube-Share"
    
    private let supportedTypes = [
        "public.mpeg-4",
        "public.movie",
        "com.apple.quicktime-movie",
        "public.avi",
        "public.video",
        "public.file-url",
        "public.url"
    ]
    
    override var nibName: NSNib.Name? {
        return NSNib.Name("ShareViewController")
    }
    
    override func loadView() {
        containerView = NSView(frame: NSRect(x: 0, y: 0, width: 300, height: 150))
        view = containerView
        
        progressIndicator = NSProgressIndicator(frame: NSRect(x: 125, y: 80, width: 32, height: 32))
        progressIndicator.style = .spinning
        progressIndicator.startAnimation(nil)
        containerView.addSubview(progressIndicator)
        
        statusLabel = NSTextField(frame: NSRect(x: 20, y: 40, width: 260, height: 20))
        statusLabel.stringValue = "Processing video..."
        statusLabel.alignment = .center
        statusLabel.isEditable = false
        statusLabel.isBordered = false
        statusLabel.backgroundColor = .clear
        containerView.addSubview(statusLabel)
        
        handleSharedItem()
    }
    
    private func handleSharedItem() {
        guard let item = self.extensionContext?.inputItems.first as? NSExtensionItem,
              let attachments = item.attachments else {
            updateStatusAndClose(with: "No video found")
            return
        }
        
        let dispatchGroup = DispatchGroup()
        var foundValidItem = false
        
        for attachment in attachments {
            dispatchGroup.enter()
            
            attachment.loadItem(forTypeIdentifier: "public.data", options: nil) { [weak self] (item, error) in
                defer { dispatchGroup.leave() }
                
                if let error = error {
                    print("Error loading data: \(error)")
                    return
                }
                
                if let data = item as? Data {
                    if let url = self?.saveVideoDataToFile(data) {
                        foundValidItem = true
                        self?.handleVideoURL(url)
                        return
                    }
                }
                
                if let url = item as? URL {
                    foundValidItem = true
                    self?.handleVideoURL(url)
                    return
                }
                
                if let urlString = item as? String,
                   let url = URL(string: urlString) {
                    foundValidItem = true
                    self?.handleVideoURL(url)
                    return
                }
            }
        }
        
        dispatchGroup.notify(queue: .main) { [weak self] in
            if !foundValidItem {
                self?.updateStatusAndClose(with: "Unsupported video format")
            }
        }
    }
    
    /// Save video to shared App Group container
    private func saveVideoDataToFile(_ data: Data) -> URL? {
        guard let sharedContainer = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: Constants.appGroupIdentifier) else {
            return nil
        }

        let fileName = "shared_video_\(Date().timeIntervalSince1970).mp4"
        let fileURL = sharedContainer.appendingPathComponent(fileName)

        do {
            try data.write(to: fileURL)
            return fileURL
        } catch {
            print("Error saving video data: \(error)")
            return nil
        }
    }
    
    private func handleVideoURL(_ url: URL) {
        
        if let sharedDefaults = UserDefaults(suiteName: Constants.appGroupIdentifier) {
            sharedDefaults.set(url, forKey: Constants.sharedVideoURLKey)  // Store URL object, not string
            sharedDefaults.set(url.lastPathComponent, forKey: Constants.sharedVideoTitleKey)
            sharedDefaults.synchronize()

            
            DispatchQueue.main.async { [weak self] in
                self?.statusLabel.stringValue = "Opening YouTube Uploader..."
            }
            
            DispatchQueue.main.async { [weak self] in
                
                if let runningApp = NSRunningApplication.runningApplications(withBundleIdentifier: self?.mainAppBundleIdentifier ?? "").first {
                    runningApp.activate()
                } else {
                    self?.launchMainApp()
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self?.closeShareExtension()
                }
            }
        } else {
            updateStatusAndClose(with: "Error saving video data")
        }
    }
    
    private func updateStatusAndClose(with message: String) {
        DispatchQueue.main.async { [weak self] in
            self?.statusLabel.stringValue = message
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                self?.closeShareExtension()
            }
        }
    }
    
    private func closeShareExtension() {
        self.extensionContext?.completeRequest(returningItems: nil, completionHandler: nil)
    }
    
    private func launchMainApp() {
        let url = URL(string: "youtube-uploader://")!
        NSWorkspace.shared.open(url)
    }
    
    @IBAction func send(_ sender: AnyObject?) {
        handleSharedItem()
    }
    
    @IBAction func cancel(_ sender: AnyObject?) {
        updateStatusAndClose(with: "Cancelled")
    }
}
