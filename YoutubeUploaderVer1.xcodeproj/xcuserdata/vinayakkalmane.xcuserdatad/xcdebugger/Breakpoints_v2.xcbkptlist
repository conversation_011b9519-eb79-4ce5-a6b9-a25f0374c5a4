<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "AC443768-FD7C-494C-8462-59BF097039D7"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "65468C38-86CE-4EF3-88A7-CE0200461E38"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "YoutubeUploaderVer1/AI-Features/CoreMLManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "30"
            endingLineNumber = "30"
            landmarkName = "generateText(prompt:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
