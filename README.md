# YouTubeUploader

## Memory-Aware AI Processing

This YouTube Uploader now includes intelligent memory management for AI operations to prevent KV cache errors and optimize performance across different system configurations.

### Features

#### 🖥️ Automatic System Detection
- **Memory Detection**: Automatically detects total and available system memory
- **Dynamic Token Allocation**: Adjusts LLM token count based on available memory
- **Memory Pressure Monitoring**: Real-time monitoring of memory usage

#### 🧠 Intelligent Text Chunking
- **Automatic Chunking**: Large transcripts are automatically split into manageable chunks
- **Context Preservation**: Maintains context between chunks with intelligent overlap
- **Sentence Boundary Respect**: Chunks are split at natural sentence boundaries
- **Progress Tracking**: Visual progress indicators for multi-chunk processing

#### 🔧 Automatic Error Recovery
- **KV Cache Error Detection**: Automatically detects "failed to find KV cache slot" errors
- **Transparent Recovery**: Automatically clears cache and retries without user intervention
- **Smart Retry Logic**: Uses safer settings (reduced tokens, chunking) for retry attempts
- **User Feedback**: Shows recovery progress with clear status messages

#### ⚙️ Memory-Based Configuration

| Available Memory | Token Count | Chunk Size | Chunking |
|-----------------|-------------|------------|----------|
| 16+ GB          | 32,768      | 4,000 chars| No       |
| 12-16 GB        | 24,576      | 4,000 chars| No       |
| 8-12 GB         | 16,384      | 4,000 chars| No       |
| 4-8 GB          | 8,192       | 2,000 chars| Yes      |
| 2-4 GB          | 4,096       | 1,000 chars| Yes      |
| < 2 GB          | 2,048       | 500 chars | Yes      |

### Implementation

#### Core Components

1. **SystemSpecsManager**: Detects system capabilities and recommends optimal settings
2. **TextChunker**: Intelligently splits large text while preserving context
3. **Enhanced LocalAIService**: Memory-aware AI processing with automatic chunking
4. **MemoryStatusView**: UI component showing real-time memory status

#### Usage

The system automatically handles memory management without requiring manual configuration:

```swift
// AI service automatically detects system specs and adjusts
let aiService = LocalAIService.shared

// Large prompts are automatically chunked if needed
await aiService.send(prompt: largeTranscript)

// Monitor memory status in UI
MemoryStatusView() // Shows current memory usage and chunking progress

// Cache management
aiService.refreshCache() // Quick cache refresh
await aiService.clearCacheAndReinitialize() // Full cache reset
```

#### Error Prevention & Recovery

- **KV Cache Errors**: Prevented by dynamic token count adjustment + automatic recovery
- **Memory Overflow**: Avoided through intelligent chunking
- **System Responsiveness**: Maintained by monitoring memory pressure
- **Automatic Recovery**: Transparent handling of cache errors with smart retry logic

### Testing

#### Safe Testing
Run the safe test suite that won't cause assertion failures:

```swift
SafeMemoryTest.runSafeTests()
```

Or for a quick verification:

```swift
SafeMemoryTest.quickTest()
```

#### Full Testing (Advanced)
For comprehensive testing (may include warnings for edge cases):

```swift
SystemSpecsTest.runTests()
```

#### Manual Testing
To enable the demo in the app, change this line in `YoutubeUploaderVer1App.swift`:
```swift
#if DEBUG && false // Change false to true to enable demo
```

### Troubleshooting

#### Runtime Assertion Errors
If you encounter assertion errors during testing:
1. Use `SafeMemoryTest.runSafeTests()` instead of the full test suite
2. The chunking algorithm handles edge cases gracefully in production
3. Assertion errors in tests don't affect the actual AI processing functionality

#### KV Cache Errors
The system automatically handles KV cache errors by:
- **Prevention**: Detecting available memory and setting appropriate token limits
- **Detection**: Monitoring for specific error patterns like "failed to find KV cache slot"
- **Recovery**: Automatically clearing cache and retrying with safer settings
- **Retry Logic**: Using reduced token counts and chunking for recovery attempts

**What you'll see during automatic recovery:**
1. `🔧 KV cache error detected. Automatically clearing cache and retrying...`
2. `🔄 Retrying after cache reset...`
3. Normal processing continues with the recovered model

**No manual intervention required** - the system handles everything automatically!

## 🎬 AI Script Writer Feature

### Overview
The AI Script Writer generates professional YouTube video scripts using your locally running LLM model. It creates structured, engaging scripts tailored to your specific topic, audience, and style preferences.

### Key Features
- **12 Video Types**: Educational, Entertainment, Tutorial, Review, Vlog, News, Gaming, Cooking, Fitness, Tech, Business, Lifestyle
- **4 Length Options**: Short (1-3 min), Medium (5-10 min), Long (15-20 min), Extended (30+ min)
- **7 Tone Styles**: Professional, Casual, Energetic, Educational, Humorous, Serious, Conversational
- **Professional Structure**: Includes hooks, timing cues, and call-to-actions
- **Local AI Processing**: Uses existing LocalAIService with memory management
- **Export Options**: Copy to clipboard or export with metadata

### Usage
1. Upload a video and enable AI Enhancement
2. Select "AI Script Writer" from the AI options
3. Configure your script preferences (topic, type, length, tone)
4. Click "Generate Script" and watch real-time progress
5. Review, edit, and export your professional script

### Integration
- Seamlessly integrates with existing AI Enhancement workflow
- Uses the same memory-aware processing as other AI features
- Leverages automatic chunking and error recovery systems
- Consistent UI/UX with other app components

For detailed documentation, see `README_AI_SCRIPT_WRITER.md`