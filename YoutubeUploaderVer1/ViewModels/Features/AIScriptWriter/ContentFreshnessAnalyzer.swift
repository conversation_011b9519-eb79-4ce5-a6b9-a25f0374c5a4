//
//  ContentFreshnessAnalyzer.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 05/01/25.
//

import Foundation
import SwiftUI

/// Represents different levels of content freshness and originality
///
/// Used to categorize YouTube content based on its uniqueness and market saturation.
/// Each category has associated scoring, visual styling, and descriptive properties.
enum FreshnessCategory: String, CaseIterable {
    case veryFresh = "Very Fresh"        // 90+ score: Highly unique content
    case fresh = "Fresh"                 // 75-89 score: Good originality
    case moderate = "Moderate"           // 60-74 score: Moderately original
    case common = "Common"               // 40-59 score: Common topic
    case oversaturated = "Oversaturated" // 0-39 score: Highly saturated

    /// The numerical score associated with this freshness category
    /// - Returns: Score value from 0-100, where higher values indicate more freshness
    var score: Double {
        switch self {
        case .veryFresh: return 90.0
        case .fresh: return 75.0
        case .moderate: return 60.0
        case .common: return 40.0
        case .oversaturated: return 0.0
        }
    }

    /// The color associated with this freshness category for UI display
    /// - Returns: SwiftUI Color representing the freshness level (green = fresh, red = oversaturated)
    var color: Color {
        switch self {
        case .veryFresh: return .green
        case .fresh: return Color(red: 0.6, green: 0.8, blue: 0.2)
        case .moderate: return .yellow
        case .common: return .orange
        case .oversaturated: return .red
        }
    }

    /// The SF Symbol icon name associated with this freshness category
    /// - Returns: String name of SF Symbol for visual representation in UI
    var icon: String {
        switch self {
        case .veryFresh: return "star.fill"
        case .fresh: return "leaf.fill"
        case .moderate: return "circle.fill"
        case .common: return "exclamationmark.triangle.fill"
        case .oversaturated: return "xmark.circle.fill"
        }
    }

    /// A human-readable description explaining what this freshness category means
    /// - Returns: Descriptive text explaining the content characteristics for this category
    var description: String {
        switch self {
        case .veryFresh: return "Highly unique content with original perspective"
        case .fresh: return "Good originality with some unique elements"
        case .moderate: return "Moderately original with standard approach"
        case .common: return "Common topic with typical treatment"
        case .oversaturated: return "Highly saturated topic needing unique angle"
        }
    }
}

/// Contains the complete analysis results for content freshness evaluation
///
/// This struct encapsulates all the data generated during the AI-powered content freshness analysis,
/// including scores, categorization, and actionable recommendations.
struct FreshnessAnalysis {
    /// Overall freshness score from 0-100, where higher values indicate more original content
    let overallScore: Double
    /// The freshness category this content falls into based on the score
    let category: FreshnessCategory
    /// List of unique elements that make the content stand out
    let uniqueElements: [String]
    /// List of common elements that are typical in this content space
    let commonElements: [String]
    /// AI-generated suggestions for improving content freshness
    let suggestions: [String]
    /// Percentage similarity to competitor content (0-100)
    let competitorSimilarity: Double
    /// Current trend status of this content topic
    let trendAlignment: String
    /// Factors that contribute to the content's originality score
    let originalityFactors: [String]
}

/// AI-powered content freshness analyzer for YouTube videos
///
/// This class uses local AI to analyze YouTube content and determine its freshness/originality
/// compared to existing content in the market. It provides detailed analysis including:
/// - Uniqueness scoring based on multiple factors
/// - Market saturation analysis
/// - Actionable improvement suggestions
/// - Trend alignment assessment
///
/// The analysis is performed in multiple steps with progress tracking and error handling.
class ContentFreshnessAnalyzer: ObservableObject {
    /// Indicates whether an analysis is currently in progress
    @Published var isAnalyzing: Bool = false
    /// The most recent analysis results, nil if no analysis has been completed
    @Published var currentAnalysis: FreshnessAnalysis?
    /// Error message if analysis fails, nil if no error
    @Published var errorMessage: String?
    /// Controls whether to show error alert in UI
    @Published var showErrorAlert: Bool = false
    /// Progress of current analysis from 0.0 to 1.0
    @Published var analysisProgress: Double = 0.0
    /// Description of the current analysis step for user feedback
    @Published var currentStep: String = ""

    /// Shared AI service instance for content analysis
    private let localAIService = LocalAIService.shared

    /// Initiates a comprehensive content freshness analysis
    ///
    /// This is the main entry point for analyzing YouTube content freshness. It validates
    /// the input and starts an asynchronous analysis process that updates the published
    /// properties with progress and results.
    ///
    /// - Parameters:
    ///   - title: The video title (required)
    ///   - description: The video description
    ///   - transcript: Optional array of timestamped transcript segments
    ///   - tags: Optional array of video tags
    ///   - category: Optional video category
    /// - Note: Updates UI through published properties; observe these for progress and results
    /// - Warning: Requires a non-empty title; shows error alert if title is missing
    func analyzeContentFreshness(
        title: String,
        description: String,
        transcript: [(TimeInterval, TimeInterval, String)] = [],
        tags: [String] = [],
        category: String = ""
    ) {
        guard !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            errorMessage = "Please provide a title to analyze"
            showErrorAlert = true
            return
        }
        
        Task {
            await performFreshnessAnalysis(
                title: title,
                description: description,
                transcript: transcript,
                tags: tags,
                category: category
            )
        }
    }
    
    @MainActor
    private func performFreshnessAnalysis(
        title: String,
        description: String,
        transcript: [(TimeInterval, TimeInterval, String)],
        tags: [String],
        category: String
    ) async {
        isAnalyzing = true
        analysisProgress = 0.0
        currentStep = "Preparing content analysis..."
        errorMessage = nil
        
        // Step 1: Extract content elements
        currentStep = "Extracting content elements..."
        analysisProgress = 0.2
        
        let contentText = extractContentText(
            title: title,
            description: description,
            transcript: transcript
        )
        
        // Step 2: Analyze uniqueness
        currentStep = "Analyzing content uniqueness..."
        analysisProgress = 0.4
        
        let uniquenessPrompt = createUniquenessAnalysisPrompt(
            title: title,
            description: description,
            contentText: contentText,
            tags: tags,
            category: category
        )
        
        await localAIService.send(prompt: uniquenessPrompt)
        let uniquenessResponse = localAIService.response
        
        // Step 3: Analyze market saturation
        currentStep = "Analyzing market saturation..."
        analysisProgress = 0.6
        
        let saturationPrompt = createSaturationAnalysisPrompt(
            title: title,
            category: category,
            tags: tags
        )
        
        await localAIService.send(prompt: saturationPrompt)
        let saturationResponse = localAIService.response
        
        // Step 4: Generate improvement suggestions
        currentStep = "Generating improvement suggestions..."
        analysisProgress = 0.8
        
        let suggestionsPrompt = createSuggestionsPrompt(
            title: title,
            description: description,
            uniquenessAnalysis: uniquenessResponse,
            saturationAnalysis: saturationResponse
        )
        
        await localAIService.send(prompt: suggestionsPrompt)
        let suggestionsResponse = localAIService.response
        
        // Step 5: Compile final analysis
        currentStep = "Compiling analysis results..."
        analysisProgress = 0.9
        
        let analysis = compileFreshnessAnalysis(
            uniquenessResponse: uniquenessResponse,
            saturationResponse: saturationResponse,
            suggestionsResponse: suggestionsResponse,
            title: title
        )
        
        currentAnalysis = analysis
        analysisProgress = 1.0
        currentStep = "Analysis complete!"
        
        isAnalyzing = false
    }
    
    private func extractContentText(
        title: String,
        description: String,
        transcript: [(TimeInterval, TimeInterval, String)]
    ) -> String {
        var contentText = "\(title)\n\n\(description)"
        
        if !transcript.isEmpty {
            let transcriptText = transcript.map { $0.2 }.joined(separator: " ")
            contentText += "\n\nTranscript: \(transcriptText)"
        }
        
        return contentText
    }
    
    private func createUniquenessAnalysisPrompt(
        title: String,
        description: String,
        contentText: String,
        tags: [String],
        category: String
    ) -> String {
        let tagsText = tags.isEmpty ? "None provided" : tags.joined(separator: ", ")
        let categoryText = category.isEmpty ? "General" : category
        
        return """
        Analyze the uniqueness and originality of this YouTube video content. Rate it on a scale of 0-100 for freshness.

        **Content to Analyze:**
        Title: \(title)
        Description: \(description)
        Category: \(categoryText)
        Tags: \(tagsText)

        **Analysis Framework:**
        1. **Topic Originality** (0-25 points):
           - Is this a completely new topic or angle?
           - How different is this from typical content in this niche?
           
        2. **Approach Uniqueness** (0-25 points):
           - Does it use a unique format or presentation style?
           - Are there innovative elements in the approach?
           
        3. **Value Proposition** (0-25 points):
           - What unique value does this provide to viewers?
           - How does it differentiate from existing content?
           
        4. **Creative Elements** (0-25 points):
           - Are there creative or innovative aspects?
           - Does it combine topics in new ways?

        **Provide your analysis in this format:**
        UNIQUENESS_SCORE: [0-100]
        UNIQUE_ELEMENTS: [List 3-5 unique aspects]
        COMMON_ELEMENTS: [List 3-5 common/typical aspects]
        ORIGINALITY_FACTORS: [List key factors that make it original or common]
        
        Be specific and analytical in your assessment.
        """
    }
    
    private func createSaturationAnalysisPrompt(
        title: String,
        category: String,
        tags: [String]
    ) -> String {
        let tagsText = tags.isEmpty ? "None provided" : tags.joined(separator: ", ")
        let categoryText = category.isEmpty ? "General" : category
        
        return """
        Analyze the market saturation level for this YouTube content topic. Consider how common this type of content is.

        **Content Details:**
        Title: \(title)
        Category: \(categoryText)
        Tags: \(tagsText)

        **Saturation Analysis:**
        1. **Topic Popularity**: How popular/common is this topic on YouTube?
        2. **Competition Level**: How many creators cover similar content?
        3. **Market Gaps**: Are there underexplored angles in this topic?
        4. **Trend Status**: Is this topic trending, stable, or declining?
        5. **Audience Demand**: How much audience demand exists for this content?

        **Provide analysis in this format:**
        SATURATION_LEVEL: [Low/Medium/High/Very High]
        COMPETITOR_SIMILARITY: [0-100] (how similar to existing content)
        TREND_STATUS: [Emerging/Growing/Stable/Declining/Oversaturated]
        MARKET_OPPORTUNITY: [High/Medium/Low]
        
        Explain your reasoning for each assessment.
        """
    }
    
    private func createSuggestionsPrompt(
        title: String,
        description: String,
        uniquenessAnalysis: String,
        saturationAnalysis: String
    ) -> String {
        return """
        Based on the uniqueness and saturation analysis, provide specific suggestions to improve content freshness.

        **Original Content:**
        Title: \(title)
        Description: \(description)

        **Previous Analysis:**
        Uniqueness Analysis: \(uniquenessAnalysis)
        Saturation Analysis: \(saturationAnalysis)

        **Provide improvement suggestions in this format:**
        FRESHNESS_SUGGESTIONS: [List 5-7 specific actionable suggestions]
        UNIQUE_ANGLE_IDEAS: [List 3-5 unique angles to explore]
        DIFFERENTIATION_STRATEGIES: [List 3-5 ways to stand out]
        
        Focus on practical, actionable advice that can immediately improve content originality.
        """
    }
    
    private func compileFreshnessAnalysis(
        uniquenessResponse: String,
        saturationResponse: String,
        suggestionsResponse: String,
        title: String
    ) -> FreshnessAnalysis {
        // Parse AI responses to extract structured data
        let uniquenessScore = extractScore(from: uniquenessResponse, key: "UNIQUENESS_SCORE") ?? 50.0
        let competitorSimilarity = extractScore(from: saturationResponse, key: "COMPETITOR_SIMILARITY") ?? 50.0
        
        // Calculate overall freshness score
        let saturationPenalty = competitorSimilarity * 0.3 // High similarity reduces freshness
        let overallScore = max(0, uniquenessScore - saturationPenalty)
        
        // Determine category
        let category = determineFreshnessCategory(score: overallScore)
        
        // Extract elements from responses
        let uniqueElements = extractList(from: uniquenessResponse, key: "UNIQUE_ELEMENTS")
        let commonElements = extractList(from: uniquenessResponse, key: "COMMON_ELEMENTS")
        let suggestions = extractList(from: suggestionsResponse, key: "FRESHNESS_SUGGESTIONS")
        let originalityFactors = extractList(from: uniquenessResponse, key: "ORIGINALITY_FACTORS")
        
        // Extract trend status
        let trendAlignment = extractValue(from: saturationResponse, key: "TREND_STATUS") ?? "Unknown"
        
        return FreshnessAnalysis(
            overallScore: overallScore,
            category: category,
            uniqueElements: uniqueElements,
            commonElements: commonElements,
            suggestions: suggestions,
            competitorSimilarity: competitorSimilarity,
            trendAlignment: trendAlignment,
            originalityFactors: originalityFactors
        )
    }
    
    private func extractScore(from text: String, key: String) -> Double? {
        let pattern = "\(key):\\s*([0-9.]+)"
        if let regex = try? NSRegularExpression(pattern: pattern),
           let match = regex.firstMatch(in: text, range: NSRange(text.startIndex..., in: text)),
           let range = Range(match.range(at: 1), in: text) {
            return Double(text[range])
        }
        return nil
    }
    
    private func extractList(from text: String, key: String) -> [String] {
        let pattern = "\(key):\\s*\\[([^\\]]+)\\]"
        if let regex = try? NSRegularExpression(pattern: pattern),
           let match = regex.firstMatch(in: text, range: NSRange(text.startIndex..., in: text)),
           let range = Range(match.range(at: 1), in: text) {
            return String(text[range])
                .components(separatedBy: ",")
                .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                .filter { !$0.isEmpty }
        }
        return []
    }
    
    private func extractValue(from text: String, key: String) -> String? {
        let pattern = "\(key):\\s*([^\\n]+)"
        if let regex = try? NSRegularExpression(pattern: pattern),
           let match = regex.firstMatch(in: text, range: NSRange(text.startIndex..., in: text)),
           let range = Range(match.range(at: 1), in: text) {
            return String(text[range]).trimmingCharacters(in: .whitespacesAndNewlines)
        }
        return nil
    }
    
    private func determineFreshnessCategory(score: Double) -> FreshnessCategory {
        switch score {
        case 90...100: return .veryFresh
        case 75..<90: return .fresh
        case 60..<75: return .moderate
        case 40..<60: return .common
        default: return .oversaturated
        }
    }
    
    func reset() {
        currentAnalysis = nil
        errorMessage = nil
        showErrorAlert = false
        analysisProgress = 0.0
        currentStep = ""
        isAnalyzing = false
    }
}
