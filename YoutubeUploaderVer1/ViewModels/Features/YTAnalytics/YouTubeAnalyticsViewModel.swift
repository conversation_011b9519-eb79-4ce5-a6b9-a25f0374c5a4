//
//  YouTubeAnalyticsViewModel.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 07/04/25.
//

import Foundation
import SwiftUI

@MainActor
class YouTubeAnalyticsViewModel: ObservableObject {
    @Published var analytics: YouTubeAnalyticsModel?
    @Published var thisWeekData :YouTubeAnalyticsModel?
    @Published var previoiusWeekData :YouTubeAnalyticsModel?
    @Published var isLoadingAnalytics: Bool = false
    @Published var isLoadingChartData: Bool = false
    @Published var errorMessage: String?
    @Published var chartData: [DailyViewData] = []
    @Published var selectedTimeRange: TimeRangeOption = .last30Days
    @Published var lifetimeRevenue: Double?
    @Published var thisWeekRevenue: Double?
    @Published var previousWeekRevenue: Double?

    
    
    private let googleSignInHelper = GoogleSignInHelper.shared
    
    //    init(googleSignInHelper: GoogleSignInHelper) {
    //        self.googleSignInHelper = googleSignInHelper
    //    }
    
    func loadAnalytics() async {
        guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
            self.errorMessage = "Your session has expired. Please sign in again."
            return
        }
        let thisWeekStart = Calendar.current.date(byAdding: .day, value: -7, to: Date())!
        let lastWeekStart = Calendar.current.date(byAdding: .day, value: -14, to: Date())!
        let lastWeekEnd = Calendar.current.date(byAdding: .day, value: -7, to: Date())!
        isLoadingAnalytics = true
        errorMessage = nil
        let data = await YouTubeAPIService.shared.fetchAnalytics(accessToken: accessToken)
        let thisWeekAnalytics = await YouTubeAPIService.shared.fetchAnalytics(accessToken: accessToken,startDate: thisWeekStart.formattedForYouTubeAPI())
        let previousWeekAnalytics = await YouTubeAPIService.shared.fetchAnalytics(accessToken: accessToken,startDate: lastWeekStart.formattedForYouTubeAPI(),endDate: lastWeekEnd.formattedForYouTubeAPI())
        self.analytics = data
        self.thisWeekData = thisWeekAnalytics
        self.previoiusWeekData = previousWeekAnalytics
        isLoadingAnalytics = false
    }
    
    func fetchViewsForSelectedRange() async {
        guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
            self.errorMessage = "Your session has expired. Please sign in again."
            self.isLoadingChartData = false
            return
        }
        
        self.isLoadingChartData = true
        self.errorMessage = nil
        self.chartData = []
        
        do{
            
            let (startDate, endDate) = calculateDateRange(for: selectedTimeRange)
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let startDateString = dateFormatter.string(from: startDate)
            let endDateString = dateFormatter.string(from: endDate)
            
            let analyticsResponse = try await YouTubeAPIService.shared.fetchTimeSeriesData(accessToken: accessToken, startDate: startDateString, endDate: endDateString)
            
            self.chartData = analyticsResponse.dailyViewData.sorted { $0.date < $1.date }
            
            // Check if the resulting processed data is empty
            if self.chartData.isEmpty {
                self.errorMessage = "No view data available for the selected period"
            }
            
        }catch let error as APIError{
            self.errorMessage = error.localizedDescription
        }catch {
            self.errorMessage = "Failed to load data: \(error.localizedDescription)"
        }
        self.isLoadingChartData = false
    }
    
    
    private func calculateDateRange(for rangeOption: TimeRangeOption) -> (start: Date, end: Date){
        let calendar = Calendar.current
        let today = Date()
        switch rangeOption {
        case .last7Days:
            let startDate = calendar.date(byAdding: .day, value: -6, to: today)!
            return (startDate, today)
        case .last30Days:
            let startDate = calendar.date(byAdding: .day, value: -30, to: today)!
            return (startDate, today)
        case .last365Days:
            let startDate = calendar.date(byAdding: .day, value: -364, to: today)!
            return (startDate, today)
        case .lifeTime:
            let startDate = Date.from(year: 2008, month: 8, day: 1)
            return (startDate, today)
            
        }
    }
    
    func loadEstimatedRevenue() async {
        guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
            self.errorMessage = "Your session has expired. Please sign in again."
            return
        }
        
        let thisWeekStart = Calendar.current.date(byAdding: .day, value: -7, to: Date())!
        let lastWeekStart = Calendar.current.date(byAdding: .day, value: -14, to: Date())!
        let lastWeekEnd = Calendar.current.date(byAdding: .day, value: -7, to: Date())!

        do {
            
            let revenue = try await YouTubeAPIService.shared.fetchRevenueAnalytics(accessToken: accessToken)
            
            let thisWeekRevenue = try await YouTubeAPIService.shared.fetchRevenueAnalytics(
                accessToken: accessToken,
                startDate: thisWeekStart.formattedForYouTubeAPI()
            )

            let lastWeekRevenue = try await YouTubeAPIService.shared.fetchRevenueAnalytics(
                accessToken: accessToken,
                startDate: lastWeekStart.formattedForYouTubeAPI(),
                endDate: lastWeekEnd.formattedForYouTubeAPI()
            )

            self.lifetimeRevenue = revenue
            self.thisWeekRevenue = thisWeekRevenue
            self.previousWeekRevenue = lastWeekRevenue

        } catch let error as APIError {
            DispatchQueue.main.async {
                self.errorMessage = error.localizedDescription
            }
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "Unexpected error: \(error.localizedDescription)"
            }
        }
    }
    


}

