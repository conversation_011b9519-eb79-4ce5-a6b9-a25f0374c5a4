//
//  YouTubeAnalyticsModel.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 07/04/25.
//

import Foundation

struct YouTubeAnalyticsModel: Codable {
    let views: Int
    let estimatedMinutesWatched: Int
    let averageViewDuration:Int
    let statistics: YouTubeChannelStatistics?
}

struct YouTubeAnalyticsResponse: Codable {
    let rows: [[Int]]
}

// Helper to decode unknown/unneeded JSON values within rows
struct AnyDecodable: Decodable {}

// Extension to skip values in an unkeyed container
extension UnkeyedDecodingContainer {
    mutating func skip() throws {
        _ = try decode(AnyDecodable.self)
    }
}

struct DailyViewData: Identifiable,Equatable {
    let id = UUID()
    let date: Date
    let viewCount: Int
    let estimatedMinutesWatched:Int
}

struct YouTubeTimeSeriesAnalyticsResponse: Decodable {
    let kind: String
    let columnHeaders: [ColumnHeader]
    let dailyViewData: [DailyViewData]
    
    private enum CodingKeys: String, CodingKey {
        case kind
        case columnHeaders
        case rows
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        self.kind = try container.decode(String.self, forKey: .kind)
        self.columnHeaders = try container.decode([ColumnHeader].self, forKey: .columnHeaders)
        
        // Initialize properties
        var processedDailyData: [DailyViewData] = []
        
        
        // Find indices of relevant columns (they might not all be present in every response)
        let dayIndex = columnHeaders.firstIndex(where: { $0.name == "day" })
        let viewsIndex = columnHeaders.firstIndex(where: { $0.name == "views" })
        let minutesIndex = columnHeaders.firstIndex(where: { $0.name == "estimatedMinutesWatched" })
        
        // --- Process Rows ---
        // Check if 'rows' key exists AND its value is not null before trying to decode as an array
        if container.contains(.rows) && (try? container.decodeNil(forKey: .rows)) == false {
            var rowsContainer = try container.nestedUnkeyedContainer(forKey: .rows)
            
            // Scenario 1: Daily Data (day dimension is present in headers)
            if let dayIdx = dayIndex, let viewIdx = viewsIndex ,let minIdx=minutesIndex{
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                dateFormatter.timeZone = TimeZone(secondsFromGMT: 0) // Use UTC or consistent timezone
                
                while !rowsContainer.isAtEnd {
                    var innerRowContainer = try rowsContainer.nestedUnkeyedContainer()
                    var dateString: String?
                    var viewCount: Int?
                    var minutesCount:Int?
                    // Decode values based on known indices
                    var rowValues: [Any?] = []
                    var tempInnerContainer = innerRowContainer // Use temporary container to iterate without consuming main one yet
                    while !tempInnerContainer.isAtEnd {
                        // Try decoding common types; append nil if fails or skipped
                        if let value = try? tempInnerContainer.decode(String.self) {
                            rowValues.append(value)
                        } else if let value = try? tempInnerContainer.decode(Int.self) {
                            rowValues.append(value)
                        } else if let value = try? tempInnerContainer.decode(Double.self) {
                            rowValues.append(value)
                        } else {
                            try? tempInnerContainer.skip() // Skip if none of the above
                            rowValues.append(nil)
                        }
                    }
                    // Now advance the actual innerRowContainer past this row's elements
                    while !innerRowContainer.isAtEnd { try innerRowContainer.skip() }
                    
                    
                    // Extract data using indices safely
                    dateString = rowValues.indices.contains(dayIdx) ? rowValues[dayIdx] as? String : nil
                    viewCount = rowValues.indices.contains(viewIdx) ? rowValues[viewIdx] as? Int : nil
                    minutesCount = rowValues.indices.contains(minIdx) ? rowValues[minIdx] as? Int : nil
                    // Validate and append DailyViewData
                    guard let validDateString = dateString,
                          let validViewCount = viewCount,
                          let estMinutesCount = minutesCount,
                          let date = dateFormatter.date(from: validDateString) else {
                        print("Warning: Skipping daily row due to missing/invalid data. DateStr: \(dateString ?? "nil"), Views: \(viewCount?.description ?? "nil")")
                        continue // Skip this row
                    }
                    processedDailyData.append(DailyViewData(date: date, viewCount: validViewCount, estimatedMinutesWatched: estMinutesCount))
                }
                
            } else {
                // This case might happen if the response has unexpected columns or is empty
                print("Warning: Response structure not recognized as daily or totals, or missing required columns (Views: \(viewsIndex != nil), Minutes: \(minutesIndex != nil), Day: \(dayIndex != nil)).")
                // Consume remaining rows if any, to avoid decoding errors later
                while !rowsContainer.isAtEnd { try? rowsContainer.skip() }
            }
        } else {
            // '.rows' key doesn't exist, or it exists but its value is null.
            print("Info: API response contained no 'rows' or 'rows' was null.")
        }
        
        // Assign the processed data (sorting daily data)
        self.dailyViewData = processedDailyData.sorted { $0.date < $1.date }
    }
}


enum TimeRangeOption: String, CaseIterable, Identifiable {
    case last7Days = "Last 7 days"
    case last30Days = "Last 1 month"
    case last365Days = "Last 1 year"
    case lifeTime = "Lifetime Views"
    
    var id: String { self.rawValue }
    var displayName: String { self.rawValue }
}

struct EstimatedRevenueAnalyticsResponse: Codable {
    let rows: [[Double]]
}


struct YouTubeChannelStatsResponse: Codable {
    let items: [YouTubeChannelItem]
}

struct YouTubeChannelItem: Codable {
    let statistics: YouTubeChannelStatistics
}

struct YouTubeChannelStatistics: Codable {
    let viewCount: String
    let subscriberCount: String
    let videoCount: String
//    let hiddenSubscriberCount: Bool
}


enum EngagementGranularity: String, CaseIterable,Identifiable {
    var id: String { self.rawValue }

    case daily, weekly, monthly

    var displayName: String {
        switch self {
        case .daily: return "Last 7 Days"
        case .weekly: return "Last 4 Weeks"
        case .monthly: return "Last 4 Months"
        }
    }

    var numberOfPeriods: Int {
        switch self {
        case .daily: return 7
        case .weekly, .monthly: return 4
        }
    }
}
