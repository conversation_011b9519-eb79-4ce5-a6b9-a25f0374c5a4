//
//  LocalAIService.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 29/05/25.
//

import Foundation
import LLM

/// A comprehensive AI service for local language model processing with intelligent memory management
///
/// This service provides:
/// - Automatic model initialization with system-appropriate token limits
/// - Intelligent text chunking for large inputs
/// - KV cache error detection and automatic recovery
/// - Memory pressure monitoring and adaptive processing
/// - Progress tracking for multi-chunk operations
///
/// The service automatically detects system capabilities and adjusts processing parameters
/// to ensure optimal performance while preventing memory-related crashes.
final class LocalAIService: ObservableObject{
    /// Shared singleton instance for app-wide AI processing
    static let shared = LocalAIService()

    /// The currently loaded language model, nil if not initialized
    @Published var model: LLM?
    /// The most recent AI response text
    @Published var response: String = ""
    /// Indicates whether AI processing is currently active
    @Published var isProcessing: Bool = false
    /// Current chunk number being processed (for multi-chunk operations)
    @Published var currentChunk: Int = 0
    /// Total number of chunks in current operation
    @Published var totalChunks: Int = 0

    /// System specifications manager for memory and performance optimization
    private let systemSpecsManager = SystemSpecsManager.shared
    /// Text chunking utility for handling large inputs
    private let textChunker = TextChunker.shared
    /// Current system specifications used for processing decisions
    private var currentSystemSpecs: SystemSpecs

    /// Initializes the AI service with current system specifications
    /// - Note: Model initialization is deferred until first use for faster app startup
    init() {
        self.currentSystemSpecs = systemSpecsManager.getSystemSpecs()
       // logSystemSpecs()
    }

//    private func logSystemSpecs() {
//        print("🖥️ System Specs:")
//        print("   Total Memory: \(String(format: "%.1f", currentSystemSpecs.totalMemoryGB)) GB")
//        print("   Available Memory: \(String(format: "%.1f", currentSystemSpecs.availableMemoryGB)) GB")
//        print("   Recommended Token Count: \(currentSystemSpecs.recommendedTokenCount)")
//        print("   Should Use Chunking: \(currentSystemSpecs.shouldUseChunking)")
//        print("   Max Chunk Size: \(currentSystemSpecs.maxChunkSize)")
//        print("   Memory Pressure: \(systemSpecsManager.getMemoryPressureLevel().description)")
//    }

    /// Initializes the language model with system-appropriate token limits
    ///
    /// This method loads the Gemma 2B model with token limits optimized for the current
    /// system's memory capacity. If initialization fails, it automatically attempts
    /// fallback initialization with reduced token limits.
    ///
    /// - Note: Refreshes system specs before initialization to ensure current memory state
    /// - Note: Uses HuggingFace model "bartowski/gemma-2-2b-it-GGUF" with Gemma template
    /// - Throws: Model loading errors are caught and handled with fallback initialization
    func initializeModel() async {
        // Refresh system specs before initialization
        currentSystemSpecs = systemSpecsManager.getSystemSpecs()

        let huggingFaceModel = HuggingFaceModel("bartowski/gemma-2-2b-it-GGUF", template:.gemma)
        do {
            print("🚀 Initializing model with \(currentSystemSpecs.recommendedTokenCount) tokens")
            if let loadedModel = try await LLM(from: huggingFaceModel, maxTokenCount: Int32(currentSystemSpecs.recommendedTokenCount)) {
                self.model = loadedModel
                print("✅ Model initialized successfully")
            }
        } catch {
            print("❌ Failed to load model: \(error)")
            // Fallback to smaller token count
            await initializeModelWithFallback()
        }
    }

    /// Attempts model initialization with reduced token limits as a fallback strategy
    ///
    /// This method is called when the primary initialization fails, typically due to
    /// insufficient memory. It uses half the recommended token count with a minimum
    /// of 8192 tokens to ensure the model can still function effectively.
    ///
    /// - Note: Fallback token count is max(8192, recommendedTokenCount / 2)
    /// - Warning: If fallback also fails, the model remains uninitialized
    private func initializeModelWithFallback() async {
        let fallbackTokenCount = max(8192, currentSystemSpecs.recommendedTokenCount / 2)
        print("🔄 Attempting fallback initialization with \(fallbackTokenCount) tokens")

        let huggingFaceModel = HuggingFaceModel("bartowski/gemma-2-2b-it-GGUF", template:.gemma)
        do {
            if let loadedModel = try await LLM(from: huggingFaceModel, maxTokenCount: Int32(fallbackTokenCount)) {
                self.model = loadedModel
                print("✅ Model initialized with fallback token count")
            }
        } catch {
            print("❌ Fallback initialization also failed: \(error)")
        }
    }

    /// Processes an AI prompt with automatic chunking and error recovery
    ///
    /// This is the main method for sending prompts to the AI model. It automatically
    /// determines whether the prompt needs to be chunked based on token limits and
    /// handles the processing accordingly. Includes built-in KV cache error detection
    /// and recovery mechanisms.
    ///
    /// - Parameter prompt: The text prompt to send to the AI model
    /// - Note: Updates published properties (response, isProcessing, etc.) during processing
    /// - Note: Automatically chunks large prompts to prevent memory issues
    /// - Warning: Requires model to be initialized; logs error if model is nil
    @MainActor
    func send(prompt: String) async {
        guard let model else {
            print("❌ Model not loaded.")
            return
        }

        // Check if we need to chunk the input
        if textChunker.needsChunking(prompt, maxTokens: currentSystemSpecs.recommendedTokenCount) {
            await sendWithChunking(prompt: prompt)
        } else {
            await sendSingle(prompt: prompt, model: model)
        }
    }

    @MainActor
    private func sendSingle(prompt: String, model: LLM) async {
        isProcessing = true
        currentChunk = 1
        totalChunks = 1
        self.response = ""

        do {
            await model.respond(to: prompt)
            self.response = model.output

            // Check for KV cache errors in the response
            if await checkForKVCacheError(response: model.output) {
                print("🚨 KV cache error detected, attempting automatic recovery...")
                await handleKVCacheError(originalPrompt: prompt)
                return
            }

        } catch {
            print("❌ Error during AI processing: \(error)")

            // Check if it's a KV cache related error
            if await isKVCacheError(error) {
                print("🚨 KV cache error detected in exception, attempting automatic recovery...")
                await handleKVCacheError(originalPrompt: prompt)
                return
            }

            self.response = "Error: \(error.localizedDescription)"
        }

        isProcessing = false
    }

    @MainActor
    private func sendWithChunking(prompt: String) async {
        guard let model else { return }

        isProcessing = true
        self.response = ""

        // Extract base prompt and content to chunk
        let (basePrompt, contentToChunk) = extractPromptComponents(prompt)

        // Create chunks
        let chunks = textChunker.chunkForAIPrompt(
            contentToChunk,
            systemSpecs: currentSystemSpecs,
            basePrompt: basePrompt
        )

        totalChunks = chunks.count
        var responses: [String] = []

        print("📝 Processing \(totalChunks) chunks...")

        for (index, chunkPrompt) in chunks.enumerated() {
            currentChunk = index + 1
            print("🔄 Processing chunk \(currentChunk)/\(totalChunks)")

            do {
                await model.respond(to: chunkPrompt)
                let chunkResponse = model.output

                // Check for KV cache errors in chunk response
                if await checkForKVCacheError(response: chunkResponse) {
                    print("🚨 KV cache error detected in chunk \(currentChunk), attempting recovery...")
                    await handleKVCacheError(originalPrompt: prompt)
                    return
                }

                responses.append(chunkResponse)

                // Update response with progress
                self.response = "Processing chunk \(currentChunk)/\(totalChunks)...\n\n" +
                               textChunker.combineChunkedResponses(responses)

            } catch {
                print("❌ Error processing chunk \(currentChunk): \(error)")

                if await isKVCacheError(error) {
                    print("🚨 KV cache error in chunk processing, attempting recovery...")
                    await handleKVCacheError(originalPrompt: prompt)
                    return
                }

                // For non-KV cache errors, continue with next chunk
                responses.append("Error in chunk \(currentChunk): \(error.localizedDescription)")
            }
        }

        // Final combined response
        self.response = textChunker.combineChunkedResponses(responses)
        isProcessing = false

        print("✅ Completed processing all chunks")
    }

    private func extractPromptComponents(_ prompt: String) -> (basePrompt: String, content: String) {
        // Look for common patterns to separate instruction from content
        let patterns = [
            "TRANSCRIPT:",
            "TEXT:",
            "CONTENT:",
            "Based on the following"
        ]

        for pattern in patterns {
            if let range = prompt.range(of: pattern, options: .caseInsensitive) {
                let basePrompt = String(prompt[..<range.upperBound])
                let content = String(prompt[range.upperBound...]).trimmingCharacters(in: .whitespacesAndNewlines)
                return (basePrompt, content)
            }
        }

        // If no pattern found, treat the whole thing as content with minimal base prompt
        return ("Analyze the following text:\n\n", prompt)
    }

    func refreshSystemSpecs() {
        currentSystemSpecs = systemSpecsManager.getSystemSpecs()
    }

    func getMemoryInfo() -> (available: Double, pressure: MemoryPressureLevel) {
        let specs = systemSpecsManager.getSystemSpecs()
        let pressure = systemSpecsManager.getMemoryPressureLevel()
        return (specs.availableMemoryGB, pressure)
    }

    func stop() {
        model?.stop()
        isProcessing = false
    }

    /// Clear the model cache and reinitialize with fresh memory settings
    @MainActor
    func clearCacheAndReinitialize() async {
        print("🧹 Clearing AI cache and reinitializing...")

        // Stop any current processing
        stop()

        // Clear the current model to free memory
        model = nil
        response = ""
        currentChunk = 0
        totalChunks = 0

        // Force garbage collection
        autoreleasepool {
            // This helps ensure memory is actually freed
        }

        // Refresh system specs to get current memory state
        refreshSystemSpecs()

        // Reinitialize the model with fresh settings
        await initializeModel()

        print("✅ Cache cleared and model reinitialized")
    }


    /// Get cache status information
    func getCacheStatus() -> (modelLoaded: Bool, memoryPressure: MemoryPressureLevel, lastResponse: String) {
        let memoryInfo = getMemoryInfo()
        return (
            modelLoaded: model != nil,
            memoryPressure: memoryInfo.pressure,
            lastResponse: response.isEmpty ? "No recent responses" : "Last response: \(response.prefix(50))..."
        )
    }

    // MARK: - KV Cache Error Detection and Handling

    /// Check if an error is related to KV cache issues
    private func isKVCacheError(_ error: Error) async -> Bool {
        let errorDescription = error.localizedDescription.lowercased()
        let kvCacheErrorPatterns = [
            "failed to find kv cache slot",
            "kv cache",
            "llama_decode: failed to decode",
            "ubatch",
            "cache slot",
            "decode failed"
        ]

        return kvCacheErrorPatterns.contains { pattern in
            errorDescription.contains(pattern)
        }
    }

    /// Check if the response contains KV cache error messages
    private func checkForKVCacheError(response: String) async -> Bool {
        let responseLower = response.lowercased()
        let kvCacheErrorPatterns = [
            "failed to find kv cache slot",
            "kv cache slot",
            "llama_decode: failed to decode",
            "decode: failed to find",
            "ubatch of size",
            "ret = 1",
            "..."
        ]

        return kvCacheErrorPatterns.contains { pattern in
            responseLower.contains(pattern)
        }
    }

    /// Handle KV cache errors with automatic recovery
    @MainActor
    private func handleKVCacheError(originalPrompt: String) async {
        print("🔧 Starting automatic KV cache error recovery...")

        // Update UI to show recovery in progress
        self.response = "🔧 KV cache error detected. Automatically clearing cache and retrying..."

        // Perform full cache reset
        await clearCacheAndReinitialize()

        // Wait a moment for the model to fully initialize
        //try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

        // Retry the original prompt with the fresh model
        if model != nil {
            print("🔄 Retrying original prompt after cache reset...")
            self.response = "🔄 Retrying after cache reset..."

            // Use chunking for the retry to be safer
            if textChunker.needsChunking(originalPrompt, maxTokens: currentSystemSpecs.recommendedTokenCount) {
                await sendWithChunking(prompt: originalPrompt)
            } else {
                // For the retry, be extra cautious and use a smaller context
                let saferSpecs = SystemSpecs(
                    totalMemoryGB: currentSystemSpecs.totalMemoryGB,
                    availableMemoryGB: currentSystemSpecs.availableMemoryGB,
                    recommendedTokenCount: currentSystemSpecs.recommendedTokenCount / 2, // Use half the tokens
                    shouldUseChunking: true,
                    maxChunkSize: currentSystemSpecs.maxChunkSize / 2
                )

                if textChunker.needsChunking(originalPrompt, maxTokens: saferSpecs.recommendedTokenCount) {
                    let chunks = textChunker.chunkForAIPrompt(originalPrompt, systemSpecs: saferSpecs)
                    await processChunksSafely(chunks: chunks, originalPrompt: originalPrompt)
                } else {
                    await model!.respond(to: originalPrompt)
                    self.response = model!.output
                }
            }

            print("✅ Recovery completed successfully")
        } else {
            self.response = "❌ Failed to recover from KV cache error. Please try again or restart the application."
            print("❌ Recovery failed - model could not be reinitialized")
        }

        isProcessing = false
    }

    /// Process chunks with extra safety measures after recovery
    @MainActor
    private func processChunksSafely(chunks: [String], originalPrompt: String) async {
        guard let model else { return }

        var responses: [String] = []
        totalChunks = chunks.count

        for (index, chunkPrompt) in chunks.enumerated() {
            currentChunk = index + 1
            print("🔄 Safely processing chunk \(currentChunk)/\(totalChunks)")

            await model.respond(to: chunkPrompt)
            let chunkResponse = model.output
            responses.append(chunkResponse)

            // Update response with progress
            self.response = "Safely processing chunk \(currentChunk)/\(totalChunks)...\n\n" +
                           textChunker.combineChunkedResponses(responses)

            // Add small delay between chunks to prevent overwhelming the cache
          //  try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        }

        // Final combined response
        self.response = textChunker.combineChunkedResponses(responses)
    }
}
