//
//  GeminiModel.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 25/05/25.
//
//

import Foundation
struct GeminiResponse: Codable {
    let candidates: [Candidate]
}

struct Candidate: Codable {
    let content: GeminiContent
}

struct GeminiContent: Codable {
    let parts: [Part]
}

struct Part: Codable {
    let text: String?
}

struct PotentialSegment: Identifiable, Codable {
    let id = UUID()
    var startTime: Double
    var endTime: Double
    let text: String
}
