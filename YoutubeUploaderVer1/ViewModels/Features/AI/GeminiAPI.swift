//
//  GeminiAPI.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 25/05/25.
//

import Foundation

class GeminiService: ObservableObject {
    @Published var responseText: String = ""
    @Published var isLoading: Bool = false
    private let apiKey: String = ""

   
    @MainActor
    func generateText(prompt: String) async {
        guard !prompt.isEmpty else {
            responseText = "Please enter a prompt."
            return
        }

        isLoading = true
        responseText = ""

        let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=\(apiKey)")!

        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ]
        ]

        guard let jsonData = try? JSONSerialization.data(withJSONObject: requestBody) else {
            responseText = "Error: Could not serialize request data."
            isLoading = false
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHT<PERSON>HeaderField: "Content-Type")
        request.httpBody = jsonData

        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                // Handle error cases (e.g., rate limits, bad request)
                let errorString = String(data: data, encoding: .utf8) ?? "Unknown error"
                print("API Error: \(errorString)") // Log for debugging
                responseText = "API Error: \(errorString)"
                isLoading = false
                return
            }

            let decodedResponse = try JSONDecoder().decode(GeminiResponse.self, from: data)

            if let content = decodedResponse.candidates.first?.content.parts.first?.text {
                responseText = content
            } else {
                responseText = "No response from Gemini."
            }
        } catch {
            print("Network Error: \(error)")
            responseText = "Network error: \(error.localizedDescription)"
        }

        isLoading = false
    }
}


