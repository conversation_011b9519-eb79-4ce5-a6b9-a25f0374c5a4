//
//  VideoUploadViewModel.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 25/04/25.
//

import Foundation
import SwiftUI
import PhotosUI

@MainActor
class VideoUploadViewModel: ObservableObject {
    var title:String = ""
    var description:String = ""
    var categoryId:String = "22"
    var privacyStatus = "Public"
    var madeForKids:Bool = false
    var notifySubscribers:Bool = false
    @Published var selectedVideoData: Data?
    @Published var showSiriDictationAlert: Bool = false
    @Published var isUploading = false
    @Published var uploadSuccess = false
    @Published var errorMessage: String?
    
    private var uploadTask: Task<Void, Never>?
    private let googleSignInHelper = GoogleSignInHelper.shared
    
    func resetUploadStatus() {
        isUploading = false
        uploadSuccess = false
        errorMessage = nil
        
    }
    
    func uploadVideo() async {
        guard let videoData = selectedVideoData else {
            errorMessage = "Please select a video"
            return
        }
        
            guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
                self.errorMessage = "Your session has expired. Please sign in again."
                return
            }
            
            let metadata = VideoUploadMetadata(
                snippet: .init(
                    title: title,
                    description: description,
                    categoryId: categoryId
                ),
                status: .init(privacyStatus: privacyStatus,
                              selfDeclaredMadeForKids: madeForKids,
                              notifySubscribers: notifySubscribers)
            )
            isUploading = true
            
            do {
                try await YouTubeAPIService.shared.uploadVideoToYoutube(
                    accessToken: accessToken,
                    metadata: metadata,
                    videoData: videoData
                )
                uploadSuccess = true
            } catch {
                    errorMessage = error.localizedDescription
            }
            
            isUploading = false
        
    }
    
    func cancelUpload() {
        uploadTask?.cancel()
        uploadTask = nil
        isUploading = false
    }
}
