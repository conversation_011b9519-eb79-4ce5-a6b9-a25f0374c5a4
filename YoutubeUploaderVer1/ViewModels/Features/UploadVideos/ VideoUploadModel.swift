//
//   VideoUploadModel.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 25/04/25.
//

import Foundation

struct VideoUploadMetadata: Codable {
    struct Snippet: Codable {
        let title: String
        let description: String
        let categoryId: String
    }

    struct Status: Codable {
        let privacyStatus: String // "private", "public", "unlisted"
        let selfDeclaredMadeForKids: Bool
        let notifySubscribers: <PERSON><PERSON>
    }

    let snippet: Snippet
    let status: Status
}
