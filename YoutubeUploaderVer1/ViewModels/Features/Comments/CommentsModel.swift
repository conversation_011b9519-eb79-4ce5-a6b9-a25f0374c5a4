//
//  CommentsModel.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON>han<PERSON> B on 15/04/25.
//

import Foundation

struct YouTubeCommentResponse: Decodable {
    let nextPageToken: String?
    let items: [CommentThread]
}

struct CommentThread: Decodable, Identifiable {
    let id: String
    let snippet: CommentThreadSnippet
    let replies: Replies?

    var videoId: String {
        snippet.topLevelComment.snippet.videoId
    }

    var textDisplay: String {
        snippet.topLevelComment.snippet.textDisplay
    }

    var authorDisplayName: String {
        snippet.topLevelComment.snippet.authorDisplayName
    }

    var authorChannelUrl: String {
        snippet.topLevelComment.snippet.authorChannelUrl
    }

    var authorChannelId: String {
        snippet.topLevelComment.snippet.authorChannelId.value
    }
}

struct CommentThreadSnippet: Decodable {
    let topLevelComment: TopLevelComment
}

struct TopLevelComment: Decodable {
    let snippet: CommentSnippet
}

struct CommentSnippet: Decodable {
    let videoId: String
    let textDisplay: String
    let authorDisplayName: String
    let authorChannelUrl: String
    let authorChannelId: AuthorChannelId
    let publishedAt: String
}

struct AuthorChannelId: Decodable {
    let value: String
}
struct Replies: Decodable {
    let comments: [Comment]
}
struct Comment: Decodable,Identifiable {
    let id: String
    let snippet: CommentSnippet
}

struct YouTubeCommentReplyRequest: Codable {
    let snippet: Snippet

    struct Snippet: Codable {
        let parentId: String
        let textOriginal: String
    }
}

struct YouTubeCommentReplyResponse: Codable {
    let id: String
    let snippet: Snippet?

    struct Snippet: Codable {
        let textDisplay: String
        let authorDisplayName: String
    }
}
