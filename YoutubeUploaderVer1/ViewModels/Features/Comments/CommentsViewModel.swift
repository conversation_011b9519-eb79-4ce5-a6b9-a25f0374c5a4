//
//  CommentsViewModel.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 15/04/25.
//

import Foundation

@MainActor
class CommentViewModel: ObservableObject {
    @Published var comments: [CommentThread] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var replySuccessMessage: String?
    @Published var isReplying:Bool = false
    private let googleSignInHelper = GoogleSignInHelper.shared

    //fetched comments
    func fetchComments(for videoId: String, pageToken: String? = nil) async {
        isLoading = true
        errorMessage = nil
        
        guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
            self.errorMessage = "Your session has expired. Please sign in again."
            self.isLoading = false
            return
        }

        do {
            let response = try await YouTubeAPIService.shared.fetchComments(videoId: videoId, accessToken: accessToken, pageToken: pageToken)
            if pageToken == nil {
                comments = response.items
            } else {
                comments.append(contentsOf: response.items)
            }
        } catch {
            errorMessage = error.localizedDescription
        }

        isLoading = false
    }
    
    //fetched comments recursively
    func fetchAllComments(for videoId: String) async {
            isLoading = true
            errorMessage = nil
            comments = []

            do {
                guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
                    errorMessage = "Your session has expired. Please sign in again."
                    isLoading = false
                    return
                }

                var pageToken: String? = nil

                repeat {
                    let response = try await YouTubeAPIService.shared.fetchComments(videoId: videoId, accessToken: accessToken, pageToken: pageToken)
                    comments.append(contentsOf: response.items)
                    pageToken = response.nextPageToken
                } while pageToken != nil

            } catch {
                errorMessage = error.localizedDescription
            }

            isLoading = false
        }
    
    //Replying to comments based on Comment Id
    func postReplyComment(commentId:String ,replyText:String) async{
        isReplying = true
        replySuccessMessage = nil
        errorMessage = nil
        do{
            guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
                errorMessage = "Your session has expired. Please sign in again."
                isLoading = false
                return
            }
            let response = try await YouTubeAPIService.shared.postCommentReply(parentId: commentId, replyText: replyText, accessToken: accessToken)
            replySuccessMessage = "Replied as \(response.snippet?.authorDisplayName ?? "Unknown")"
        }
        catch{
            errorMessage = "Failed to post reply: \(error.localizedDescription)"
        }
        isReplying = false
    }
}
