//
//  CommentClassifierViewModel.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 15/04/25.
//

import Foundation
import CoreML

class CommentClassifierViewModel: ObservableObject {
    private let model: CommentsClassifier
    private let commentGeneratorModel: Auto_reply_bot
    private let classifier = try! CommentsClassifier(configuration: MLModelConfiguration())
    @Published var inputText: String = ""
    @Published var sentimentLabel: String = ""
    @Published var generatedComment :String = ""
    @Published var positiveCount = 0
    @Published var negativeCount = 0
    @Published var neutralCount = 0
    @Published var positiveComment:[CommentThread] = []
    @Published var negativeComment:[CommentThread] = []
    @Published var neutralComment:[CommentThread] = []
    @Published var isGeneratingReply:Bool = false
    init() {
        do {
            self.model = try CommentsClassifier(configuration: MLModelConfiguration())
            self.commentGeneratorModel = try Auto_reply_bot(configuration: MLModelConfiguration())
        } catch {
            fatalError("Failed to load model: \(error)")
        }
    }

    func classifyComment() {
        do {
            let prediction = try model.prediction(text: inputText)
            DispatchQueue.main.async {
                self.sentimentLabel = prediction.label
            }
        } catch {
            print("Prediction error: \(error)")
            sentimentLabel = "Error"
        }
    }
    

    func autogenerateComment(comment: String, using localService: LocalAIService) async -> String {
        isGeneratingReply = true

        let prompt = """
        You are an AI assistant. Respond constructively to the following user comment:

        \"\(comment)\"

        Make sure your reply is helpful, polite, concise, and contextually relevant. Only provide one short reply.Avoid inserting quotes while replying
        """

        await localService.send(prompt: prompt)

        isGeneratingReply = false
        return  localService.response.isEmpty ? "Could not generate a response." : localService.response
    }
    
    

    func analyzeComments(_ comments: [CommentThread]) {
            var pos = 0, neg = 0, neu = 0

            for comment in comments {
                let text = comment.textDisplay.lowercased()
                if let prediction = try? classifier.prediction(text: text) {
                    switch prediction.label.lowercased() {
                    case "positive": pos += 1
                        positiveComment.append(comment)
                    case "negative": neg += 1
                        negativeComment.append(comment)
                    case "neutral": neu += 1
                        neutralComment.append(comment)
                    default: break
                    }
                }
            }
            DispatchQueue.main.async {
                self.positiveCount = pos
                self.negativeCount = neg
                self.neutralCount = neu
            }
        }
    
    
}
