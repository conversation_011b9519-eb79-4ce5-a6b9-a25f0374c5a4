//
//  VideoSummary.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 26/05/25.
//

import Foundation
import AppKit
import SwiftUI


class VideoSummaryViewModel: ObservableObject{
    @Published var summary: String = ""
    @Published var relatedSegments: String?
    @Published var isLoading: Bool = false
    @Published var isExtractingSegments: Bool = false
    let localAIService = LocalAIService.shared
    
    func generateVideoSummary(from transcript: [(TimeInterval, TimeInterval, String)], summaryLength: SummaryLength) async {
        guard !transcript.isEmpty else {
            summary = "Transcript is empty."
            return
        }
        
        let instruction: String
        switch summaryLength {
        case .brief:
            instruction = "Generate a **concise** one-liner description about the video content."
        case .standard:
            instruction = "Generate a **medium-length summary** describing the main ideas of the video as a human would."
        case .detailed:
            instruction = "Generate a **detailed summary**, covering the key points and context from the transcript like a human summarizer."
        }
        
        let prompt = """
        \(instruction)
        
        Transcript:
        \(transcript)
        """
        
        isLoading = true
        //await localAIService.initializeModel()
        
        await localAIService.send(prompt: prompt)
        summary = localAIService.response
        
        isLoading = false
    }
    
    //    func getRelatedTranscriptSegments(from transcript: [(TimeInterval, TimeInterval, String)], keyword: String) async {
    //        guard !transcript.isEmpty else {
    //            relatedSegments = "Transcript is empty."
    //            return
    //        }
    //
    //        let instruction = """
    //        Given the following transcript, extract and return only those segments that are contextually related to the keyword: "\(keyword)".
    //
    //        Do not rely only on exact word matches—include semantically or thematically related segments as well. For example, for the keyword "war", segments mentioning "conflict", "battle", "invasion", or "military attack" should also be included.
    //
    //        Maintain the original structure of each segment as a dictionary with keys "startTime", "endTime", and "text". If two or more consecutive segments are all related, return them together in the final list. Do not modify or paraphrase the text.
    //        """
    //
    //        let formattedTranscript = transcript.map {
    //            """
    //            {
    //                "startTime": \($0.0),
    //                "endTime": \($0.1),
    //                "text": "\($0.2.replacingOccurrences(of: "\"", with: "\\\""))"
    //            }
    //            """
    //        }.joined(separator: ",\n")
    //
    //        let prompt = """
    //        \(instruction)
    //
    //        Transcript:
    //        [
    //        \(formattedTranscript)
    //        ]
    //        """
    //
    //        print(prompt)
    //
    //        isExtractingSegments = true
    //
    //        await localAIService.send(prompt: prompt)
    //        self.relatedSegments = localAIService.response
    //        print("This is the response for related segments", relatedSegments)
    //
    //        isExtractingSegments = false
    //    }
    func getRelatedTranscriptSegments(from transcript: [(TimeInterval, TimeInterval, String)], keyword: String,clipDuration:Int) async {
        guard !transcript.isEmpty else {
            relatedSegments = "Transcript is empty."
            return
        }
        
        let instruction = """
        You are a smart segment extractor. Given a transcript, extract ONLY the segments that are directly focused on the topic of the natural language query such as: "\(keyword)".
        
        Be STRICT. Only include segments where the subject matter is clearly about the natural language query or closely related ideas.
        ✔ the segment must clearly address the query's meaning.
        ✔ the segments must add up to or must fall under \(clipDuration) seconds.
        
        
        ✘ DO NOT include segments just because they are in the same general video.
        ✘ DO NOT include loosely related themes, future speculation, or unrelated projects.
        ✔ Only include a segment if someone reading it would say "Yes, this is about \(keyword)".
        ✔ And also if the rearranging of segments makes a meaningful set of segments with continuation then you are free to do it.
        
        Return results in JSON array with:
        [
          {
            "startTime": Double,
            "endTime": Double,
            "text": String
          }
        ]
        
        Do not modify the text. Keep it exactly as in the transcript.
        """
        
        let formattedTranscript = transcript.map {
            """
            {
                "startTime": \($0.0),
                "endTime": \($0.1),
                "text": "\($0.2.replacingOccurrences(of: "\"", with: "\\\""))"
            }
            """
        }.joined(separator: ",\n")
        
        let prompt = """
        \(instruction)
        
        Transcript:
        [
        \(formattedTranscript)
        ]
        """
        
                isExtractingSegments = true
                await localAIService.send(prompt: prompt)
                self.relatedSegments = localAIService.response
                print("Extracted Related Segments:\n\(String(describing: relatedSegments))")
                isExtractingSegments = false
        
//        isExtractingSegments = true
//        await localAIService.send(prompt: prompt)
//        
//        
//        // Parse and clean the response
//        let rawResponse = localAIService.response
//        print("Extracted Related Segments:\n\(String(describing: rawResponse))")
//        let segments = extractPotentialSegments(from: rawResponse)
//        
//        guard !segments.isEmpty else {
//            relatedSegments = "No relevant segments found or failed to decode."
//            isExtractingSegments = false
//            return
//        }
//        
//        // Filter segments within max duration
//        var selectedSegments: [PotentialSegment] = []
//        var accumulatedDuration: TimeInterval = 0
//        let maxDuration = TimeInterval(clipDuration)
//        
//        for segment in segments {
//            let duration = segment.endTime - segment.startTime
//            if accumulatedDuration + duration <= maxDuration {
//                selectedSegments.append(segment)
//                accumulatedDuration += duration
//            } else {
//                break
//            }
//        }
//        
//        // Convert to JSON string
//        let encodedSegments = selectedSegments.map { seg -> [String: Any] in
//            [
//                "startTime": seg.startTime,
//                "endTime": seg.endTime,
//                "text": seg.text
//            ]
//        }
//        
//        if let data = try? JSONSerialization.data(withJSONObject: encodedSegments, options: .prettyPrinted),
//           let jsonString = String(data: data, encoding: .utf8) {
//            self.relatedSegments = jsonString
//        } else {
//            self.relatedSegments = "Failed to encode selected segments."
//        }
//        
//        print("Extracted Related Segments:\n\(String(describing: relatedSegments))")
//        isExtractingSegments = false
    }
    
    
}
