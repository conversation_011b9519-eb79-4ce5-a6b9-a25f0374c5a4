
//
//  YouTubeVideoAnalyticsViewModel.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> <PERSON> on 08/04/25.
//

import Foundation
import SwiftUI
import CoreFoundation

@MainActor
class YouTubeVideoAnalyticsViewModel: ObservableObject {
    @Published var videoAnalytics: VideoAnalyticsModel?
    @Published var videoEngagementAnalytics: EngagementAnalyticsResponse?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var uploadId: String?
    @Published var allUploadedVideos: [YouTubeVideo]?
    @Published var selectedGranularity: EngagementGranularity = .daily
    @Published var engagementGraphData: [(label: String, views: Int, likes: Int, comments: Int, shares: Int)] = []
    @Published var topVideo: YouTubeVideo?
    @Published var selectedVideo:YouTubeVideo?

    var nextPageToken: String? = nil
    
    @Published var viewsData: [VideoMetricData] = []
    @Published var likesData: [VideoMetricData] = []
    @Published var dislikesData: [VideoMetricData] = []
    @Published var commentsData: [VideoMetricData] = []
    @Published var sharesData: [VideoMetricData] = []



    private let googleSignInHelper: GoogleSignInHelper

    init(googleSignInHelper: GoogleSignInHelper, selectedVideo: YouTubeVideo? = nil) {
        self.googleSignInHelper = googleSignInHelper
        self.selectedVideo = selectedVideo
    }
    
    func videoTapped(_ video: YouTubeVideo) {
        selectedVideo = video
    }

    func loadVideoAnalytics(for videoId: String) async {
        isLoading = true
        errorMessage = nil

        guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
            self.errorMessage = "Your session has expired. Please sign in again."
            self.isLoading = false
            return
        }

        let result = await YouTubeAPIService.shared.fetchVideoAnalytics(videoId: videoId, accessToken: accessToken)

        if let analytics = result {
            self.videoAnalytics = analytics
        } else {
            self.errorMessage = "Failed to load video analytics."
        }

        isLoading = false
    }

    func loadVideoEngagementAnalytics(for videoId: String,startDate:String,endDate:String) async {
        isLoading = true
        errorMessage = nil

        guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
            self.errorMessage = "Your session has expired. Please sign in again."
            self.isLoading = false
            return
        }

        let result = await YouTubeAPIService.shared.fetchEngagementMetrics(videoId: videoId, accessToken: accessToken, startDate: startDate, endDate: endDate)

        if let analytics = result {
            self.videoEngagementAnalytics = analytics
        } else {
            self.errorMessage = "Failed to load video analytics."
        }

        isLoading = false
    }


    
    func getUploadsPlayListId() async -> String? {
        guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
            self.errorMessage = "Your session has expired. Please sign in again."
            return nil
        }
        print(accessToken)


        let uploadId = await YouTubeAPIService.shared.fetchUploadsPlaylistId(accessToken: accessToken)
        
        if let uploadId = uploadId {
            self.uploadId = uploadId
            return uploadId
        } else {
            self.errorMessage = "Failed to get the uploads Id"
            return nil
        }
    }

    
    
    func loadUserVideos(using playlistId: String) async {
        guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
            self.errorMessage = "Your session has expired. Please sign in again."
            return
        }

        isLoading = true
        errorMessage = nil

        if let response = await YouTubeAPIService.shared.fetchUploadedVideos(accessToken: accessToken, playlistId: playlistId) {
            let (videos, nextPageToken) = response
            var enrichedVideos: [YouTubeVideo] = []

            await withTaskGroup(of: YouTubeVideo.self) { group in
                for video in videos {
                    group.addTask {
                        var enrichedVideo = video
                        if let analytics = await YouTubeAPIService.shared.fetchVideoAnalytics(videoId: video.videoId, accessToken: accessToken) {
                            enrichedVideo.analytics = analytics
                        }
                        return enrichedVideo
                    }
                }

                for await enrichedVideo in group {
                    enrichedVideos.append(enrichedVideo)
                }
            }
            
            let sortedByRecent = enrichedVideos.sorted {
                guard let date1 = ISO8601DateFormatter().date(from: $0.publishedAt),
                      let date2 = ISO8601DateFormatter().date(from: $1.publishedAt) else {
                    return false
                }
                return date1 > date2
            }

            self.allUploadedVideos = sortedByRecent
            self.nextPageToken = nextPageToken
        } else {
            self.errorMessage = "Failed to get the uploaded videos"
        }

        isLoading = false
    }
    
    func loadNextPage(using playlistId: String) async {
        guard let accessToken = await googleSignInHelper.getValidAccessToken(), !isLoading else {
            return
        }

        isLoading = true
        errorMessage = nil

        // Fetch next page of videos using the nextPageToken
        if let pageToken = nextPageToken {
            if let response = await YouTubeAPIService.shared.fetchUploadedVideos(accessToken: accessToken, playlistId: playlistId, pageToken: pageToken) {
                let (videos, nextPageToken) = response
                var enrichedVideos: [YouTubeVideo] = []

                // Enrich videos with analytics in parallel using task group
                await withTaskGroup(of: YouTubeVideo.self) { group in
                    for video in videos {
                        group.addTask {
                            var enrichedVideo = video
                            if let analytics = await YouTubeAPIService.shared.fetchVideoAnalytics(videoId: video.videoId, accessToken: accessToken) {
                                enrichedVideo.analytics = analytics
                            }
                            return enrichedVideo
                        }
                    }

                    // Collect enriched videos from the task group
                    for await enrichedVideo in group {
                        enrichedVideos.append(enrichedVideo)
                    }
                }

                // Sort by published date (recent first)
                let sortedByRecent = enrichedVideos.sorted {
                    guard let date1 = ISO8601DateFormatter().date(from: $0.publishedAt),
                          let date2 = ISO8601DateFormatter().date(from: $1.publishedAt) else {
                        return false
                    }
                    return date1 > date2
                }

                // Append the new videos to the existing list
                self.allUploadedVideos?.append(contentsOf: sortedByRecent)
                self.nextPageToken = nextPageToken

            } else {
                self.errorMessage = "Failed to get the uploaded videos"
            }
        } else {
            self.errorMessage = "No more videos to load"
        }

        isLoading = false
    }
    
    func fetchEngagementMetricsForLast7Days(videoId: String, accessToken: String) async -> VideoAnalyticsModel? {
        
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .day, value: -7, to: endDate)!
        let start = startDate.formattedForYouTubeAPI()
        let end = endDate.formattedForYouTubeAPI()

        // Fetch the metrics for the last 7 days
        if let analyticsResponse = await YouTubeAPIService.shared.fetchVideoAnalytics(
            videoId: videoId,
            accessToken: accessToken,
            startDate: start,
            endDate: end
        ) {
                // Return the enriched analytics data
                return VideoAnalyticsModel(
                    videoId: videoId,
                    views: analyticsResponse.views,
                    likes: analyticsResponse.likes,
                    comments: analyticsResponse.comments,
                    shares: analyticsResponse.shares,
                    averageViewDuration: analyticsResponse.averageViewDuration,
                    averageViewPercentage: analyticsResponse.averageViewPercentage,
                    subscribersGained: analyticsResponse.subscribersGained,
                    subscribersLost: analyticsResponse.subscribersLost,
                    engagementRate: analyticsResponse.engagementRate,
                    retentionRate: analyticsResponse.retentionRate
                )
        }
        return nil
    }

    func calculateTrendingScore(analytics: VideoAnalyticsModel) -> Double {
        // Example formula for calculating the trending score
        return Double(analytics.views) * 0.5 + Double(analytics.likes) * 0.3 + Double(analytics.comments) * 0.2
    }

    func getTopTrendingVideo(using playlistId: String) async -> YouTubeVideo? {
        guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
            self.errorMessage = "Session expired. Please sign in again."
            return nil
        }

        isLoading = true
        defer { isLoading = false }

        var allVideos: [YouTubeVideo] = []
        var pageToken: String? = nil

        // Fetch all pages of videos
        repeat {
            if let response = await YouTubeAPIService.shared.fetchUploadedVideos(accessToken: accessToken, playlistId: playlistId, pageToken: pageToken) {
                let (videos, nextToken) = response
                allVideos.append(contentsOf: videos)
                pageToken = nextToken
            } else {
                self.errorMessage = "Failed to fetch uploaded videos"
                break
            }
        } while pageToken != nil

        // Enrich with analytics and trending score
        var enrichedVideos: [YouTubeVideo] = []

        await withTaskGroup(of: YouTubeVideo?.self) { group in
            for video in allVideos {
                group.addTask {
                    var enriched = video
                    if let analytics = await self.fetchEngagementMetricsForLast7Days(videoId: video.videoId, accessToken: accessToken) {
                        enriched.analytics = analytics
                        enriched.trendingScore = await self.calculateTrendingScore(analytics: analytics)
                        return enriched
                    }
                    return nil
                }
            }

            for await enriched in group {
                if let enriched = enriched {
                    enrichedVideos.append(enriched)
                }
            }
        }


        let topTrending = enrichedVideos.max { ($0.trendingScore ?? 0) < ($1.trendingScore ?? 0) }
        self.topVideo = topTrending
        return topTrending
    }



    
    func fetchUploadsAndVideos() async {
        if let uploadId = await getUploadsPlayListId() {
            await loadUserVideos(using: uploadId)
        }
    }
    
    func getTopTrendingVideo() async {
        if let uploadId = await getUploadsPlayListId() {
            self.topVideo = await getTopTrendingVideo(using: uploadId)
        }
    }
    
    func fetchNextVideos() async {
        if let uploadId = await getUploadsPlayListId() {
            await loadNextPage(using: uploadId)
        }
    }
    
    func loadEngagementGraph(videoId: String) async {
        isLoading = true
        errorMessage = nil
        guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
            errorMessage = "Session expired. Sign in again."
            isLoading = false
            return
        }

        engagementGraphData.removeAll()
        viewsData.removeAll()      // Clear existing data
        likesData.removeAll()
        dislikesData.removeAll()
        commentsData.removeAll()
        sharesData.removeAll()
        let calendar = Calendar.current
        let today = Date()

        for i in 0..<selectedGranularity.numberOfPeriods {
            let start: Date
            let end: Date
            let label: String

            switch selectedGranularity {
            case .daily:
                start = calendar.date(byAdding: .day, value: -i, to: today)!
                end = start
                label = DateFormatter.shortDate.string(from: start)
                
                //            case .weekly:
                //                end = calendar.date(byAdding: .weekOfYear, value: -i, to: today)!
                //                start = calendar.date(from: calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: end))!
                //                label = "W\(calendar.component(.weekOfYear, from: start))"
            case .weekly:
                // Start from Monday of this week - i weeks ago
                if let referenceDate = calendar.date(byAdding: .weekOfYear, value: -i, to: today),
                   let weekStart = calendar.dateInterval(of: .weekOfYear, for: referenceDate)?.start {
                    
                    start = weekStart
                    end = calendar.date(byAdding: .day, value: 6, to: start)!
                    label = "\(DateFormatter.shortDate.string(from: start)) - \(DateFormatter.shortDate.string(from: end))"
                } else {
                    continue
                }
                
                
            case .monthly:
                if i == 0 {
                    // Current month up to today
                    start = calendar.date(from: calendar.dateComponents([.year, .month], from: today))!
                    end = today
                } else {
                    // Full month in the past
                    let monthDate = calendar.date(byAdding: .month, value: -i, to: today)!
                    start = calendar.date(from: calendar.dateComponents([.year, .month], from: monthDate))!
                    end = calendar.date(byAdding: DateComponents(month: 1, day: -1), to: start)! // end of the month
                }
                
                label = DateFormatter.shortMonth.string(from: start)
            }
            
//            // MARK: - Simulated Dummy Data Based on Granularity
//            let baseViews = 1000 + i * 200
//            let baseLikes = Int(Double(baseViews) * 0.1) + Int.random(in: 5...20)
//            let baseDisLikes = Int(Double(baseViews) * 0.1) + Int.random(in: 5...20)
//            let baseComments = Int(Double(baseViews) * 0.03) + Int.random(in: 2...10)
//            let baseShares = Int(Double(baseViews) * 0.02) + Int.random(in: 1...5)
//            
//            engagementGraphData.append((label: label, views: baseViews, likes: baseLikes, comments: baseComments, shares: baseShares))
//            viewsData.append(VideoMetricData(label: label, value: baseViews))
//            likesData.append(VideoMetricData(label: label, value: baseLikes))
//            dislikesData.append(VideoMetricData(label: label, value: baseDisLikes))
//            commentsData.append(VideoMetricData(label: label, value: baseComments))
//            sharesData.append(VideoMetricData(label: label, value: baseShares))


            let result = await YouTubeAPIService.shared.fetchEngagementMetrics(videoId: videoId, accessToken: accessToken, startDate: start.formattedForYouTubeAPI(), endDate: end.formattedForYouTubeAPI())

            if let row = result?.rows.first {
                engagementGraphData.append((label: label, views: row.views, likes: row.likes, comments: row.comments, shares: row.shares))
                viewsData.append(VideoMetricData(label: label, value: row.views))
                likesData.append(VideoMetricData(label: label, value: row.likes))
                dislikesData.append(VideoMetricData(label: label, value: row.dislikes))
                commentsData.append(VideoMetricData(label: label, value: row.comments))
                sharesData.append(VideoMetricData(label: label, value: row.shares))
                
            } 
        }

        engagementGraphData.reverse()
        viewsData.reverse()
        likesData.reverse()
        dislikesData.reverse()
        commentsData.reverse()
        sharesData.reverse()
        isLoading = false
    }


}
