//
//  VideoAnalyticsModel.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 08/04/25.
//

import Foundation


struct VideoAnalyticsModel:Codable,Hashable {
    let videoId: String
    let views: Int
    let likes: Int
    let comments: Int
    let shares: Int
    let averageViewDuration: Int
    let averageViewPercentage: Double
    let subscribersGained: Int
    let subscribersLost: Int
    let engagementRate: Double
    let retentionRate: Double
}

struct YouTubeVideo: Identifiable,Hashable {
    let id = UUID()
    let title: String
    let description: String
    let publishedAt: String
    let thumbnailURL: String
    let playlistId: String
    let videoId: String
    var analytics: VideoAnalyticsModel?
    var trendingScore:Double?
}




struct EngagementAnalyticsResponse:Codable {
    let kind: String
    let columnHeaders: [ColumnHeader]
    let rows: [VideoStatRow]
}

struct ColumnHeader: Codable {
    let name: String
    let columnType: String
    let dataType: String
}


struct VideoStatRow: Codable {
    let videoId: String
    let views: Int
    let likes: Int
    let dislikes: Int
    let comments: Int
    let shares: Int

    init(from decoder: Decoder) throws {
        var container = try decoder.unkeyedContainer()
        self.videoId = try container.decode(String.self)
        self.views = try container.decode(Int.self)
        self.likes = try container.decode(Int.self)
        self.dislikes = try container.decode(Int.self)
        self.comments = try container.decode(Int.self)
        self.shares = try container.decode(Int.self)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.unkeyedContainer()
        try container.encode(videoId)
        try container.encode(views)
        try container.encode(likes)
        try container.encode(dislikes)
        try container.encode(comments)
        try container.encode(shares)
    }
}



struct AverageDurationAndViewAnalyticsResponse:Codable {
    let kind: String
    let columnHeaders: [ColumnHeader]
    let rows: [AverageViewRow]
}

struct AverageViewRow: Codable {
    let videoId: String
    let averageViewDuration: Int
    let averageViewPercentage: Double

    init(from decoder: Decoder) throws {
        var container = try decoder.unkeyedContainer()
        self.videoId = try container.decode(String.self)
        self.averageViewDuration = try container.decode(Int.self)
        self.averageViewPercentage = try container.decode(Double.self)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.unkeyedContainer()
        try container.encode(videoId)
        try container.encode(averageViewDuration)
        try container.encode(averageViewPercentage)
    }
}




struct SubscribersGainedAnalyticsResponse: Codable {
    let kind: String
    let columnHeaders: [ColumnHeader]
    let rows: [SubscribersGainedRow]
}


struct SubscribersGainedRow: Codable {
    let videoId: String
    let subscribersGained: Int
    let subscribersLost:Int

    init(from decoder: Decoder) throws {
        var container = try decoder.unkeyedContainer()
        self.videoId = try container.decode(String.self)
        self.subscribersGained = try container.decode(Int.self)
        self.subscribersLost = try container.decode(Int.self)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.unkeyedContainer()
        try container.encode(videoId)
        try container.encode(subscribersGained)
        try container.encode(subscribersLost)
    }
}



//for the uploads id from the first url
struct ChannelListResponse: Codable {
    let items: [ChannelItem]
}

struct ChannelItem: Codable {
    let contentDetails: ContentDetails
}

struct ContentDetails: Codable {
    let relatedPlaylists: RelatedPlaylists
    
}

struct RelatedPlaylists: Codable {
    let uploads: String
}


//get all videos api second one
struct UserVideosResponse: Codable {
    let items: [YouTubeAPIItem]
    let nextPageToken:String?
}

struct YouTubeAPIItem: Codable {
    let snippet: Snippet
    let contentDetails: VideoContentDetails
}

struct Snippet: Codable {
    let publishedAt: String
    let channelId: String
    let title: String
    let description: String
    let thumbnails: Thumbnails
    let playlistId: String?
    let resourceId: ResourceId?
    let localized: Localized?
}

struct Thumbnails: Codable {
    let `default`: Thumbnail
    let standard : Thumbnail?
}

struct Localized:Codable {
    let title:String
    let description:String
}
struct Thumbnail: Codable {
    let url: String
    let width: Int?
    let height: Int?
}
struct ResourceId: Codable {
    let videoId: String
}

struct VideoContentDetails: Codable {
    let videoId: String
}

struct VideoMetricData: Identifiable {
    let id = UUID()
    let label: String // Could be date or week/month label
    let value: Int
}
