//
//  PlaylistsModel.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON>hashan<PERSON> B on 11/04/25.
//

import Foundation

struct PlaylistResponse: Codable {
    let items: [Playlist]
    let nextPageToken: String?
}

struct Playlist: Codable, Identifiable {
    let id: String
    let snippet: Snippet
    let contentDetails: PlaylistContentDetails
}

struct PlaylistContentDetails: Codable {
    let itemCount: Int
}


//info abouyt the playlist
struct PlaylistItemsResponse: Codable {
    let items: [PlaylistItem]
}

struct PlaylistItem: Codable {
    let id: String
    let snippet: Snippet
}





