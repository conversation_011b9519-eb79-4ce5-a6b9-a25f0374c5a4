//
//  PlaylistsViewModel.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON>han<PERSON> B on 11/04/25.
//

import Foundation

@MainActor
class PlaylistViewModel: ObservableObject {
    static let shared = PlaylistViewModel()
    @Published var playlists: [Playlist] = []
    @Published var selectedPlaylistVideos: [PlaylistItem] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var enrichedPlaylistVideos: [YouTubeVideo] = []
    @Published var selectedPlaylist: Playlist?
    var nextPageToken: String? = nil


    


    private let googleSignInHelper = GoogleSignInHelper.shared

   
    
    func playlistTapped(_ playlist: Playlist) {
        selectedPlaylist = playlist
    }
    
    // MARK: - Fetch All Playlists
    func fetchUserPlaylists() async {
        isLoading = true
        errorMessage = nil

        guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
            self.errorMessage = "Your session has expired. Please sign in again."
            self.isLoading = false
            return
        }

        do {
            let response = try await YouTubeAPIService.shared.fetchAllPlayLists(accessToken: accessToken)
            let (playlists,nextPageToken) = response
            
            //this gets all platlists including the personal ones
//            self.playlists = playlists.items
            
            //this filters out the personal based on observation not 100% sure
            self.playlists = playlists.items.filter { playlist in
                if let localized = playlist.snippet.localized {
                    return !localized.description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
                }
                return false
            }

            self.nextPageToken = nextPageToken
        } catch {
            self.errorMessage = "Failed to fetch playlists: \(error.localizedDescription)"
        }

        isLoading = false
    }
    
    func loadNextPage() async {
        guard let accessToken = await googleSignInHelper.getValidAccessToken(), !isLoading else {
            return
        }

        isLoading = true
        errorMessage = nil

        // Fetch the next page of playlists using the nextPageToken
        if let pageToken = nextPageToken {
            do {
                let response = try await YouTubeAPIService.shared.fetchAllPlayLists(accessToken: accessToken, pageToken: pageToken)
                let (playlists, nextPageToken) = response

                //this gets all platlists including the personal ones
//                self.playlists.append(contentsOf: playlists.items)
                
                
                //this filters out the personal based on observation not 100% sure
                let userCreatedPlaylists = playlists.items.filter { playlist in
                    
                    if let localized = playlist.snippet.localized {
                        return !localized.description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
                               
                    }
                    return false
                }
                self.playlists.append(contentsOf: userCreatedPlaylists)

                self.nextPageToken = nextPageToken

            } catch {
                self.errorMessage = "Failed to load next page of playlists: \(error.localizedDescription)"
            }
        } else {
            self.errorMessage = "No more playlists to load"
        }

        isLoading = false
    }



    
    func fetchVideosFromPlaylist() async {
        
        guard let playlistId = selectedPlaylist?.id else {
            errorMessage = "No playlist selected"
            return
        }
        
        isLoading = true
        errorMessage = nil
        enrichedPlaylistVideos = []

        guard let accessToken = await googleSignInHelper.getValidAccessToken() else {
            self.errorMessage = "Your session has expired. Please sign in again."
            self.isLoading = false
            return
        }

        do {
            let response = try await YouTubeAPIService.shared.fetchPlaylistItems(playlistId: playlistId, accessToken: accessToken)
            self.selectedPlaylistVideos = response.items
            
            var result: [YouTubeVideo] = []

            for item in response.items {
                guard var video = mapToYouTubeVideo(from: item) else { continue }

                if let analytics = await YouTubeAPIService.shared.fetchVideoAnalytics(videoId: video.videoId, accessToken: accessToken) {
                    video.analytics = analytics
                }

                result.append(video)
            }
            self.enrichedPlaylistVideos = result

        } catch {
            self.errorMessage = "Failed to fetch videos for the playlist: \(error.localizedDescription)"
        }

        isLoading = false
    }

    
    private func mapToYouTubeVideo(from item: PlaylistItem) -> YouTubeVideo? {
        let snippet = item.snippet
        let thumbnailURL = snippet.thumbnails.standard?.url ?? snippet.thumbnails.default.url
        guard
            let videoId = snippet.resourceId?.videoId,
            let playlistId = snippet.playlistId
        else {
            return nil
        }

        return YouTubeVideo(
            title: snippet.title,
            description: snippet.description.isEmpty == false ? snippet.description : TextConstants.Playlists.NoDesc,
            publishedAt: snippet.publishedAt,
            thumbnailURL: thumbnailURL,
            playlistId: playlistId,
            videoId: videoId,
            analytics: nil
        )
    }

}
