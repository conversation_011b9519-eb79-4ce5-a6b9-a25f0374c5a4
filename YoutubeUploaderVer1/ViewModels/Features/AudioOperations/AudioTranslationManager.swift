//
//  AudioTranslationManager.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 05/01/25.
//

import Foundation
import AVFoundation
import SwiftUI

//enum SupportedLanguage: String, CaseIterable {
//    case hindi = "hi-IN"
//    case english = "en-US"
//    
//    var displayName: String {
//        switch self {
//        case .hindi:
//            return "Hindi"
//        case .english:
//            return "English"
//        }
//    }
//    
//    var iconName: String {
//        switch self {
//        case .hindi:
//            return "globe.asia.australia"
//        case .english:
//            return "globe.americas"
//        }
//    }
//}

class AudioTranslationManager: NSObject, ObservableObject {
    @Published var isTranslating: Bool = false
    @Published var isPlaying: Bool = false
    @Published var translatedAudioURL: URL?
    @Published var selectedLanguage: SupportedLanguage = .hindi
    @Published var errorMessage: String?
    @Published var showErrorAlert: Bool = false
    @Published var translationProgress: Double = 0.0
    @Published var translatedText: String = ""
    @Published var translationStep: TranslationStep = .idle

    private var speechSynthesizer: AVSpeechSynthesizer?
    private var currentUtterance: AVSpeechUtterance?
    private let localAIService = LocalAIService.shared
    
    override init() {
        super.init()
        setupSpeechSynthesizer()
    }
    
    private func setupSpeechSynthesizer() {
        speechSynthesizer = AVSpeechSynthesizer()
        speechSynthesizer?.delegate = self
    }
    
    func translateAndSynthesizeAudio(from transcript: [(TimeInterval, TimeInterval, String)],
                                   to language: SupportedLanguage,
                                   completion: @escaping (URL?) -> Void) {
        guard !transcript.isEmpty else {
            DispatchQueue.main.async {
                self.errorMessage = "No transcript available for translation"
                self.showErrorAlert = true
                completion(nil)
            }
            return
        }

        DispatchQueue.main.async {
            self.isTranslating = true
            self.selectedLanguage = language
            self.translationProgress = 0.0
            self.translationStep = .translatingText
            self.errorMessage = nil
            self.translatedText = ""
        }

        // Combine all transcript text
        let fullText = transcript.map { $0.2 }.joined(separator: " ")

        // Step 1: Translate text using AI model
        Task {
            await translateTextWithAI(text: fullText, to: language) { [weak self] translatedText in
                guard let self = self, let translatedText = translatedText else {
                    DispatchQueue.main.async {
                        self?.errorMessage = "Failed to translate text"
                        self?.showErrorAlert = true
                        self?.isTranslating = false
                        completion(nil)
                    }
                    return
                }

                DispatchQueue.main.async {
                    self.translatedText = translatedText
                    self.translationStep = .synthesizingSpeech
                    self.translationProgress = 0.5 // 50% complete after translation
                }

                // Step 2: Convert translated text to speech
                self.synthesizeTranslatedText(translatedText, language: language, completion: completion)
            }
        }
    }

    private func translateTextWithAI(text: String, to language: SupportedLanguage, completion: @escaping (String?) -> Void) async {
        let prompt = createTranslationPrompt(text: text, targetLanguage: language)

        // Use the existing LocalAIService
        await localAIService.send(prompt: prompt)

        // Get the response
        let translatedText = localAIService.response

        // Clean up the response (remove any extra formatting)
        let cleanedText = cleanTranslationResponse(translatedText)

        completion(cleanedText.isEmpty ? nil : cleanedText)
    }

    private func createTranslationPrompt(text: String, targetLanguage: SupportedLanguage) -> String {
        let languageName = targetLanguage.displayName

        return """
        Translate the following English text to \(languageName).

        Instructions:
        - Provide ONLY the translated text, no explanations or additional content
        - Maintain the original meaning and context
        - Use natural, conversational \(languageName)
        - Do not include phrases like "Here is the translation:" or similar

        Text to translate:
        \(text)

        Translation:
        """
    }

    private func cleanTranslationResponse(_ response: String) -> String {
        // Remove common AI response prefixes and clean up the text
        let cleanedText = response
            .replacingOccurrences(of: "Translation:", with: "")
            .replacingOccurrences(of: "Here is the translation:", with: "")
            .replacingOccurrences(of: "The translation is:", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)

        return cleanedText
    }

    private func synthesizeTranslatedText(_ text: String, language: SupportedLanguage, completion: @escaping (URL?) -> Void) {
        // Create speech utterance with translated text
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = AVSpeechSynthesisVoice(language: language.rawValue)
        utterance.rate = 0.5 // Slightly slower for better clarity
        utterance.pitchMultiplier = 1.0
        utterance.volume = 1.0

        // Store current utterance for progress tracking
        currentUtterance = utterance

        // Create temporary file for output
        let tempDirectory = FileManager.default.temporaryDirectory
        let outputURL = tempDirectory.appendingPathComponent("translated_audio_\(language.rawValue)_\(Date().timeIntervalSince1970).caf")

        // Start synthesis (audio will be played directly)
        synthesizeToFile(utterance: utterance, outputURL: outputURL) { [weak self] success in
            DispatchQueue.main.async {
                self?.isTranslating = false
                self?.translationStep = success ? .completed : .idle
                self?.translationProgress = success ? 1.0 : 0.0

                if success {
                    // Audio is played directly during synthesis
                    completion(nil) // No file URL since we're playing directly
                } else {
                    self?.errorMessage = "Failed to generate translated audio"
                    self?.showErrorAlert = true
                    completion(nil)
                }
            }
        }
    }
    
    private func synthesizeToFile(utterance: AVSpeechUtterance, outputURL: URL, completion: @escaping (Bool) -> Void) {
        // For macOS, we'll use a simpler approach - just speak the utterance and let the user hear it
        // File recording on macOS requires more complex setup with audio units

        // Store completion for delegate callback
        self.synthesisCompletion = completion

        // Start speaking
        speechSynthesizer?.speak(utterance)
    }

    private var synthesisCompletion: ((Bool) -> Void)?
    
    func playTranslatedAudio() {
        guard let audioURL = translatedAudioURL else { return }
        
        do {
            let audioPlayer = try AVAudioPlayer(contentsOf: audioURL)
            audioPlayer.play()
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "Failed to play translated audio: \(error.localizedDescription)"
                self.showErrorAlert = true
            }
        }
    }
    
    func stopAudio() {
        speechSynthesizer?.stopSpeaking(at: .immediate)
        DispatchQueue.main.async {
            self.isPlaying = false
            self.isTranslating = false
            self.translationStep = .idle
        }
    }

    func reset() {
        isTranslating = false
        isPlaying = false
        translatedAudioURL = nil
        selectedLanguage = .hindi
        errorMessage = nil
        showErrorAlert = false
        translationProgress = 0.0
        translatedText = ""
        translationStep = .idle
        currentUtterance = nil
    }
}

// MARK: - AVSpeechSynthesizerDelegate
extension AudioTranslationManager: AVSpeechSynthesizerDelegate {
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didStart utterance: AVSpeechUtterance) {
        DispatchQueue.main.async {
            self.isPlaying = true
            self.isTranslating = false
            self.translationStep = .completed
            self.translationProgress = 0.1
        }
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, willSpeakRangeOfSpeechString characterRange: NSRange, utterance: AVSpeechUtterance) {
        let progress = Double(characterRange.location) / Double(utterance.speechString.count)
        DispatchQueue.main.async {
            self.translationProgress = min(0.9, progress)
        }
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        DispatchQueue.main.async {
            self.isPlaying = false
            self.translationProgress = 1.0
            self.translationStep = .idle
            // Call completion if available
            self.synthesisCompletion?(true)
            self.synthesisCompletion = nil
        }
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        DispatchQueue.main.async {
            self.isPlaying = false
            self.isTranslating = false
            self.translationProgress = 0.0
            self.translationStep = .idle
            // Call completion with failure
            self.synthesisCompletion?(false)
            self.synthesisCompletion = nil
        }
    }
}
