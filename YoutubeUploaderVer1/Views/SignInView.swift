//
//  SignInView.swift
//  YoutubeUploaderVer1
//
//  Created by Shashank B on 03/04/25.
//

import SwiftUI

struct SignInView: View {
    @State private var email: String = ""
    @State private var password: String = ""
    @State private var isPasswordVisible: Bool = false
    @State private var rememberMe: Bool = false
    @EnvironmentObject var googleSignInHelper: GoogleSignInHelper


       
    var body: some View {
        if let user = googleSignInHelper.user {
            DashboardView(signedInUser: user)
        }else {
            ZStack {
                Color.darkBackgroundColor.edgesIgnoringSafeArea(.all)
                
                // Main VStack
                VStack {
                    // Top Logo
                    ZStack {
                        Circle()
                            .fill(AppColor.iconColor.color)
                            .frame(width: 80, height: 80)
                        
                        Image(systemName: "play.rectangle.fill")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 40, height: 40)
                            .foregroundColor(AppColor.primary.color)
                    }
                    .padding(.vertical)
                    
                    // Main content area with dark background
                    VStack(spacing: 25) {
                        // Title
                        VStack(spacing: 8) {
                            Text(TextConstants.SignIn.title)
                                .font(.system(size: 28, weight: .bold))
                                .foregroundColor(AppColor.primary.color)
                            
                            Text(TextConstants.SignIn.subtitle)
                                .font(.system(size: 16))
                                .foregroundColor(AppColor.grayText.color)
                        }
                        
                        // Form fields
                        VStack(spacing: 15) {
//
                            // Google sign in
                            Button(action: googleSignInHelper.signIn) {
                                HStack {
                                    Image(systemName: "g.circle.fill")
                                        .foregroundColor(.white)
                                    
                                    Text(TextConstants.SignIn.googleButton)
                                        .foregroundColor(.white)
                                        .font(.system(size: 16))
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(AppColor.youtubeRed.color)
                                .cornerRadius(8)
                            }
                            .buttonStyle(PlainButtonStyle())
                            
                            
                            // Terms
                            Text(TextConstants.SignIn.termsAndPrivacy)
                                .foregroundColor(AppColor.grayText.color)
                                .font(.system(size: 12))
                                .multilineTextAlignment(.center)
                                .padding(.top, 10)
                        }
                        .padding(.horizontal, 20)
                    }
                    .padding(.vertical)
                    .frame(width: 500)
                    .cornerRadius(12)
                }
                .background(RoundedRectangle(cornerRadius: /*@START_MENU_TOKEN@*/25.0/*@END_MENU_TOKEN@*/)
                    .fill(AppColor.darkGrayBackground.color))
                .padding(40)
            }
        }
    }
}



