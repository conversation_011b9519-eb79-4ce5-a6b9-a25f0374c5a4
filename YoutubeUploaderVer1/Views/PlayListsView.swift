//
//  PlayListsView.swift
//  YoutubeUploaderVer1
//
//  Created by Shashan<PERSON> B on 09/04/25.
//

import SwiftUI


struct PlayListsView: View {
    @EnvironmentObject var navigationCoordinator:NavigationCoordinator
    @StateObject private var viewModel = PlaylistViewModel.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            
            modernHeaderSection
            ScrollView {
                if viewModel.isLoading {
                    VStack(spacing: 16) {
                        ForEach(0..<3, id: \.self) { _ in
                            HStack(spacing: 16) {
                                ForEach(0..<4, id: \.self) { _ in
                                    CardViewSkeleton()
                                        .blinking(duration: 0.75)
                                }
                            }
                        }
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }else if let error = viewModel.errorMessage {
                    Text("⚠️ \(error)")
                        .foregroundColor(.red)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if viewModel.playlists.isEmpty {
                    Spacer()
                    HStack {
                        Spacer()
                        modernEmptySection
                        Spacer()
                    }
                    Spacer()
                }
                else{
                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16)
                    ], spacing: 16) {
                        ForEach(viewModel.playlists) { playlist in
                            Button(action: {
                                viewModel.playlistTapped(playlist)
                                navigationCoordinator.navigateToPlaylistDetailView()
                                
                            }) {
                                PlaylistCard(playlist: playlist)
                            }
                            .buttonStyle(PlainButtonStyle())
                            
                        }
                    }
                    .padding(.bottom, 16)
                }
                
                if let _ = viewModel.nextPageToken, !viewModel.isLoading , viewModel.playlists.count >= 8{
                    Button(action: {
                        Task {
                            await viewModel.loadNextPage()
                        }
                    }) {
                        HStack {
                            Text("Load more")
                                .font(AppFontStyle.callout.style.bold())
                                .foregroundStyle(AppColor.youtubeRed.color)
                            Image(systemName: "arrow.down.circle.fill")
                                .foregroundStyle(AppColor.youtubeRed.color)
                        }
                        
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .clipShape(RoundedRectangle(cornerRadius: 10))
                        .shadow(color: AppColor.youtubeRed.color.opacity(0.3), radius: 6, x: 0, y: 2)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .padding(.trailing, 30)
                    .frame(maxWidth: .infinity, alignment: .trailing)
                }
            }
            
        }
        .frame(minWidth: 800, minHeight: 600)
        .padding(.horizontal, 24)
        .task {
            await viewModel.fetchUserPlaylists()
        }
        
    }
    
    private var modernHeaderSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack(alignment: .top) {
                // Title and Subtitle
                VStack(alignment: .leading, spacing: 8) {
                    Text(TextConstants.Playlists.playlistsHeaderTitle)
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [AppColor.textPrimary.color, AppColor.accentBlue.color],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                    
                    Text(TextConstants.Playlists.playlistsHeaderSubtitle)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }
                
                Spacer()
            }
        }
        .padding(.top, 16)
        .padding(.bottom, 24)
        
    }
    
    private var modernEmptySection: some View {
        VStack(spacing: 24) {
            // Icon with gradient background
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [AppColor.youtubeRed.color.opacity(0.1), AppColor.accentBlue.color.opacity(0.1)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)

                Image(systemName: "music.note.list")
                    .font(.system(size: 44, weight: .medium))
                    .foregroundColor(AppColor.youtubeRed.color)
            }

            VStack(spacing: 12) {
                Text(TextConstants.Playlists.noPlaylistsTitle)
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Text(TextConstants.Playlists.noPlaylistsDescription)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.vertical, 60)
    }
}



// Extensions to help with date formatting if needed
extension String {
    func toDate() -> Date? {
        let formatter = ISO8601DateFormatter()
        return formatter.date(from: self)
    }
}

extension Date {
    func timeAgo() -> String {
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day, .weekOfMonth, .month], from: self, to: now)
        
        if let month = components.month, month > 0 {
            return month == 1 ? TextConstants.Playlists.oneMonthAgo : "\(month) \(TextConstants.Playlists.monthsAgo)"
        } else if let week = components.weekOfMonth, week > 0 {
            return week == 1 ? TextConstants.Playlists.oneWeekAgo : "\(week) \(TextConstants.Playlists.weeksAgo)"
        } else if let day = components.day, day > 0 {
            return day == 1 ? TextConstants.Playlists.oneDayAgo : "\(day) \(TextConstants.Playlists.daysAgo)"
        } else {
            return TextConstants.Playlists.today
        }
    }
}


#Preview {
    PlayListsView()
}
