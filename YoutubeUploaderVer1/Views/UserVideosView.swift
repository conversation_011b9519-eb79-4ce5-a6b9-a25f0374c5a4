//
//  MyVideosList.swift
//  YoutubeUploaderVer1
//
//  Created by Shashan<PERSON> B on 07/04/25.
//

import SwiftUI
import AVKit

struct UserVideosView: View {
    @EnvironmentObject var videoAnalyticsViewModel: YouTubeVideoAnalyticsViewModel
    @State private var searchText: String = ""
    @State private var isSearchFocused: Bool = false
    @EnvironmentObject var navigationCoordinator: NavigationCoordinator

    private var filteredVideos: [YouTubeVideo] {
        guard let allVideos = videoAnalyticsViewModel.allUploadedVideos else { return [] }
        if searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return allVideos
        }
        return allVideos.filter { video in
            video.title.lowercased().contains(searchText.lowercased())
        }
    }

    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Modern Header Section
            modernHeaderSection

            // Content Section
            contentSection
        }
        .task {
            await videoAnalyticsViewModel.fetchUploadsAndVideos()
        }
    }

    // MARK: - Modern Header Section
    private var modernHeaderSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack(alignment: .top) {
                // Title and Subtitle
                VStack(alignment: .leading, spacing: 8) {
                    Text(TextConstants.UserVideos.title)
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [AppColor.textPrimary.color, AppColor.accentBlue.color],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text(TextConstants.UserVideos.subtitle)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }

                Spacer()

                // Modern Search Bar
                modernSearchBar
            }

        }
        .padding(.horizontal, 24)
        .padding(.top, 16)
        .padding(.bottom, 24)
    }
    // MARK: - Modern Search Bar
    private var modernSearchBar: some View {
        HStack(spacing: 12) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(isSearchFocused ? AppColor.youtubeRed.color : AppColor.textSecondary.color)
                .animation(.easeInOut(duration: 0.2), value: isSearchFocused)

            TextField("Search videos...", text: $searchText)
                .font(.system(size: 15, weight: .medium))
                .foregroundColor(AppColor.textPrimary.color)
                .textFieldStyle(PlainTextFieldStyle())
                .onFocusChange { focused in
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isSearchFocused = focused
                    }
                }

            if !searchText.isEmpty {
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        searchText = ""
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }
                .buttonStyle(PlainButtonStyle())
                .transition(.scale.combined(with: .opacity))
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.surfacePrimary.color)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(
                            isSearchFocused ? AppColor.youtubeRed.color.opacity(0.5) : AppColor.borderPrimary.color.opacity(0.3),
                            lineWidth: isSearchFocused ? 2 : 1
                        )
                        .animation(.easeInOut(duration: 0.2), value: isSearchFocused)
                )
        )
        .frame(width: 380)
        .shadow(color: isSearchFocused ? AppColor.youtubeRed.color.opacity(0.1) : Color.clear, radius: 8, x: 0, y: 2)
        .animation(.easeInOut(duration: 0.2), value: isSearchFocused)
    }


    // MARK: - Content Section
    private var contentSection: some View {
        VStack(spacing: 0) {
            if videoAnalyticsViewModel.isLoading {
                modernLoadingSection
            } else if let error = videoAnalyticsViewModel.errorMessage {
                modernErrorSection(error: error)
            } else if filteredVideos.isEmpty {
                modernEmptySection
            } else {
                modernVideoListSection
            }
        }
        .padding(.horizontal, 24)
    }
    

    // MARK: - Modern Loading Section
    private var modernLoadingSection: some View {
        VStack(spacing: 20) {
            ForEach(0..<4, id: \.self) { _ in
                modernVideoSkeletonView
            }
        }
        .padding(.vertical, 20)
    }

    // MARK: - Modern Error Section
    private func modernErrorSection(error: String) -> some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 48, weight: .medium))
                .foregroundColor(AppColor.youtubeRed.color)

            VStack(spacing: 8) {
                Text("Something went wrong")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Text(error)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }

            Button(action: {
                Task {
                    await videoAnalyticsViewModel.fetchUploadsAndVideos()
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 14, weight: .semibold))
                    Text("Try Again")
                        .font(.system(size: 14, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(AppColor.youtubeRed.color)
                .cornerRadius(8)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.vertical, 60)
    }

    // MARK: - Modern Empty Section
    private var modernEmptySection: some View {
        VStack(spacing: 24) {
            // Icon with gradient background
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [AppColor.youtubeRed.color.opacity(0.1), AppColor.accentBlue.color.opacity(0.1)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)

                Image(systemName: searchText.isEmpty ? "film.stack" : "magnifyingglass")
                    .font(.system(size: 48, weight: .medium))
                    .foregroundColor(AppColor.youtubeRed.color)
            }

            VStack(spacing: 12) {
                Text(searchText.isEmpty ? "No Videos Yet" : "No Videos Found")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Text(searchText.isEmpty ? "Upload your first video to get started" : "Try adjusting your search terms")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }

            if searchText.isEmpty {
                Button(action: {
                    navigationCoordinator.navigateToVideoUploadPage()
                }) {
                    HStack(spacing: 10) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 16, weight: .semibold))
                        Text("Upload Your First Video")
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 14)
                    .background(
                        LinearGradient(
                            colors: [AppColor.youtubeRed.color, AppColor.youtubeRed.color.opacity(0.8)],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
                    .shadow(color: AppColor.youtubeRed.color.opacity(0.3), radius: 8, x: 0, y: 4)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.vertical, 60)
    }

    // MARK: - Modern Video List Section
    private var modernVideoListSection: some View {
        VStack(spacing: 0) {
            ScrollView {
                LazyVStack(spacing: 20) {
                    ForEach(filteredVideos) { video in
                        VideoItemView(video: video, isVideos: true)
                            .transition(.opacity.combined(with: .scale(scale: 0.95)))
                    }
                }
                .padding(.vertical, 20)
            }

            // Modern Load More Button
            if searchText.isEmpty, let _ = videoAnalyticsViewModel.nextPageToken, !videoAnalyticsViewModel.isLoading {
                modernLoadMoreButton
            }
        }
    }

    // MARK: - Modern Load More Button
    private var modernLoadMoreButton: some View {
        Button(action: {
            Task {
                await videoAnalyticsViewModel.fetchNextVideos()
            }
        }) {
            HStack(spacing: 12) {
                Image(systemName: "arrow.down.circle.fill")
                    .font(.system(size: 18, weight: .semibold))

                Text("Load More Videos")
                    .font(.system(size: 16, weight: .semibold))
            }
            .foregroundColor(AppColor.youtubeRed.color)
            .padding(.horizontal, 24)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(AppColor.surfacePrimary.color)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(AppColor.youtubeRed.color.opacity(0.3), lineWidth: 2)
                    )
            )
            .shadow(color: AppColor.youtubeRed.color.opacity(0.1), radius: 8, x: 0, y: 4)
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.vertical, 20)
    }

    // MARK: - Modern Stat Card
    private func modernStatCard(icon: String, color: Color, title: String, value: String, subtitle: String) -> some View {
        HStack(spacing: 12) {
            // Icon with background
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
                .frame(width: 48, height: 48)
                .overlay(
                    Image(systemName: icon)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(color)
                )

            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Text(title)
                    .font(.system(size: 12, weight: .semibold))
                    .foregroundColor(AppColor.textSecondary.color)
                    .textCase(.uppercase)
                    .tracking(0.5)

                Text(subtitle)
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(AppColor.textTertiary.color)
            }

            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.surfacePrimary.color.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }

    // MARK: - Modern Video Skeleton View
    private var modernVideoSkeletonView: some View {
        HStack(spacing: 0) {
            // Thumbnail skeleton
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.surfacePrimary.color)
                .frame(width: 280, height: 160)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
                .padding(.leading, 20)
                .padding(.vertical, 20)

            // Content skeleton
            VStack(alignment: .leading, spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    // Title skeleton
                    RoundedRectangle(cornerRadius: 6)
                        .fill(AppColor.surfacePrimary.color)
                        .frame(height: 20)
                        .frame(maxWidth: .infinity)

                    RoundedRectangle(cornerRadius: 6)
                        .fill(AppColor.surfacePrimary.color)
                        .frame(height: 16)
                        .frame(maxWidth: 200)
                }

                // Metrics skeleton
                LazyVGrid(
                    columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 4),
                    spacing: 12
                ) {
                    ForEach(0..<8, id: \.self) { _ in
                        RoundedRectangle(cornerRadius: 8)
                            .fill(AppColor.surfacePrimary.color)
                            .frame(height: 60)
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 20)
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(AppColor.surfacePrimary.color.opacity(0.5))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(AppColor.borderPrimary.color.opacity(0.1), lineWidth: 1)
                )
        )
        .blinking(duration: 1.5)
    }

    // MARK: - Helper Functions
    private func formatLargeNumber(_ number: Int) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal

        if number >= 1_000_000 {
            return String(format: "%.1fM", Double(number) / 1_000_000)
        } else if number >= 1_000 {
            return String(format: "%.1fK", Double(number) / 1_000)
        } else {
            return formatter.string(from: NSNumber(value: number)) ?? "0"
        }
    }
}

// MARK: - Focus Change Extension
extension View {
    func onFocusChange(_ action: @escaping (Bool) -> Void) -> some View {
        self.onReceive(NotificationCenter.default.publisher(for: NSControl.textDidBeginEditingNotification)) { _ in
            action(true)
        }
        .onReceive(NotificationCenter.default.publisher(for: NSControl.textDidEndEditingNotification)) { _ in
            action(false)
        }
    }
}


