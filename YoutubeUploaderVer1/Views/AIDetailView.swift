//
//  VideoSummarizationDetailView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 26/05/25.
//

import SwiftUI

struct AIDetailView: View {
    let option: AIOption
    let transcriptItems: [(TimeInterval, TimeInterval, String)]
    let selectedFileURL: URL?
    let videoTitle: String
    let videoDescription: String
    let videoCategory: String
    @State private var showCopyAlert = false
    @StateObject private var viewModel = VideoSummaryViewModel()
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
//            Text(option.title)
//                .font(AppFontStyle.title2.style.weight(.bold))
//                .foregroundColor(AppColor.primary.color)
            
            // Example content based on the AI option selected
            switch option {
            case .videoSummarization:
                VideoSummarizationView(
                    viewModel: viewModel,
                    transcriptItems: transcriptItems
                )
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("VideoSummarizationStarted"))) { _ in
                    NotificationCenter.default.post(name: NSNotification.Name("AIProcessStarted"), object: "videoSummarization")
                }
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("VideoSummarizationCompleted"))) { _ in
                    NotificationCenter.default.post(name: NSNotification.Name("AIProcessCompleted"), object: "videoSummarization")
                }

            case .contentFreshness:
                ContentFreshnessView(
                    title: videoTitle,
                    description: videoDescription,
                    transcript: transcriptItems,
                    tags: [],
                    category: videoCategory
                )
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ContentFreshnessStarted"))) { _ in
                    NotificationCenter.default.post(name: NSNotification.Name("AIProcessStarted"), object: "contentFreshness")
                }
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ContentFreshnessCompleted"))) { _ in
                    NotificationCenter.default.post(name: NSNotification.Name("AIProcessCompleted"), object: "contentFreshness")
                }

            case .performancePredictor:
                PerformancePredictorView(
                    title: videoTitle,
                    description: videoDescription,
                    category: videoCategory,
                    thumbnailDescription: ""
                )
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("PerformancePredictionStarted"))) { _ in
                    NotificationCenter.default.post(name: NSNotification.Name("AIProcessStarted"), object: "performancePredictor")
                }
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("PerformancePredictionCompleted"))) { _ in
                    NotificationCenter.default.post(name: NSNotification.Name("AIProcessCompleted"), object: "performancePredictor")
                }

            case .shortsClips, .contentRecreation:
                ShortsClipsCreationView(
                    videoURL: selectedFileURL,
                    viewModel: viewModel,
                    transcriptItems: transcriptItems
                )
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ShortsClipsStarted"))) { _ in
                    NotificationCenter.default.post(name: NSNotification.Name("AIProcessStarted"), object: "shortsClips")
                }
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ShortsClipsCompleted"))) { _ in
                    NotificationCenter.default.post(name: NSNotification.Name("AIProcessCompleted"), object: "shortsClips")
                }
            }
        }
        .padding()
//        .background(
//            RoundedRectangle(cornerRadius: 16)
//                .fill(AppColor.darkGrayBackground.color)
//        )
        .transition(.opacity.combined(with: .move(edge: .top)))
    }
    
}


