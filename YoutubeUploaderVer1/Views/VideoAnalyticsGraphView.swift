//
//  VideoAnalyticsGraphView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 18/04/25.
//

import SwiftUI
import Charts

struct VideoAnalyticsGraphView: View {
    @EnvironmentObject var navigationCoordinator:NavigationCoordinator
    @StateObject private var viewModel: YouTubeVideoAnalyticsViewModel
    private var isVideos:Bool = true
    
    
    
    init(video: YouTubeVideo,isVideos: Bool = true) {
        self.isVideos = isVideos
        _viewModel = StateObject(wrappedValue: YouTubeVideoAnalyticsViewModel(
                    googleSignInHelper: GoogleSignInHelper(),
                    selectedVideo: video
                ))
    }
    
    var body: some View {
        ScrollView {
            VStack(alignment:.leading,spacing: 20) {
                CustomButton(text: "Back to \(isVideos ? "Videos" : "Playlist")") {
                    navigationCoordinator.goBackOneStep()
                }

                
                HStack{
                    VStack(alignment: .leading, spacing: 2) {
                        Text(viewModel.selectedVideo?.title ?? "Title")
                            .font(AppFontStyle.largeTitle.style)
                        HStack {
                            Text("Published on: \(formattedTimeAgo(from:viewModel.selectedVideo?.publishedAt ?? ""))")
                                .font(AppFontStyle.callout.style)
                                .foregroundColor(.gray)
                        }
                        .padding(.top, 8)
                    }
                    .padding(.bottom, 16)
                    Spacer()
                    Menu {
                        ForEach(EngagementGranularity.allCases) { option in
                            Button(option.displayName) {
                                viewModel.selectedGranularity = option
                                Task {
                                    await viewModel.loadEngagementGraph(videoId:viewModel.selectedVideo?.videoId ?? "")
                                }
                            }
                        }
                    }
                   
                label:{
                    Text(viewModel.selectedGranularity.displayName)
                    
                }
                .onAppear{
                    Task{
                        await viewModel.loadEngagementGraph(videoId: viewModel.selectedVideo?.videoId ?? "")
                    }
                }
                .background(AppColor.darkBackground.color)
                .frame(width:200)
                .controlSize(.large)
                    
                    
                }
                
                
                
                Divider()
                
                // MARK: - Loading and Error States
              if let error = viewModel.errorMessage {
                    Text(error)
                        .foregroundColor(AppColor.youtubeRed.color)
                        .padding()
                }

                
                VStack(alignment: .leading, spacing: 16) {
                    if viewModel.isLoading{
                        ForEach(0..<4){_ in
                            MetricChartSkeleton()
                                .padding()
                                .blinking(duration: 0.75)
                                .frame(height: 400)
                            
                        }
                    }
                    else{
                        VStack(alignment: .leading, spacing: 16) {
                            
                            // Full width chart for Views
                            MetricChart(title: "Views", data: viewModel.viewsData, color: .blue)
                            
                            // Two charts in a row: Likes and Dislikes
                            HStack(alignment: .top, spacing: 16) {
                                MetricChart(title: "Likes", data: viewModel.likesData, color: .green)
                                MetricChart(title: "Dislikes", data: viewModel.dislikesData, color: .red)
                            }
                            
                            // Two charts in a row: Comments and Shares
                            HStack(alignment: .top, spacing: 16) {
                                MetricChart(title: "Comments", data: viewModel.commentsData, color: .orange)
                                MetricChart(title: "Shares", data: viewModel.sharesData, color: .purple)
                            }
                            
                            CommentsClassifierView(videoId: viewModel.selectedVideo?.videoId ?? "")
                        }
                        .padding()
                        .frame(maxWidth: .infinity)

                    }
                }
                .padding()
            }
        }
        .task {
            await viewModel.loadEngagementGraph(videoId: viewModel.selectedVideo?.videoId ?? "")
        }
    }
}
