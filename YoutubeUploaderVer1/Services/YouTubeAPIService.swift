//
//  YouTubeAPIService.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 07/04/25.
//

import Foundation

final class YouTubeAPIService{
    static let shared = YouTubeAPIService()
    private init() {}
    
    
    //gets total view coutn toatl waatch time not in real time
    func fetchAnalytics(accessToken: String,startDate: String? = nil, endDate: String? = nil) async -> YouTubeAnalyticsModel?{
        let start = startDate ?? "2006-08-01"
        let end = endDate ?? Date().formattedForYouTubeAPI()
        let urlString = "\(APIConstants.baseURL)/v2/reports?ids=channel==MINE&metrics=views,estimatedMinutesWatched,averageViewDuration&startDate=\(start)&endDate=\(end)"
        let statsurlString = "\(APIConstants.youtubeBaseURL)/channels?part=statistics&mine=true"
        do{
            let analytics: YouTubeAnalyticsResponse = try await NetworkManager.shared.fetch(
                urlString: urlString,
                accessToken: accessToken
            )
            guard let analyticsData = analytics.rows.first, analyticsData.count >= 2 else {
                print("Error: Analytics data is missing or incomplete")
                return nil
            }
            
            
            //gets toatal view subcribers count and videos count in real time
            let statsResponse: YouTubeChannelStatsResponse = try await NetworkManager.shared.fetch(
                urlString: statsurlString,
                accessToken: accessToken
            )
            let stats = statsResponse.items.first?.statistics
            
            let model = YouTubeAnalyticsModel(
                views: analyticsData[0],
                estimatedMinutesWatched: analyticsData[1],
                averageViewDuration:analyticsData[2],
                statistics: stats
            )
            return model
        }
        catch let error as APIError {
            print("API Error: \(error.localizedDescription)")
        } catch {
            print("Unexpected error: \(error.localizedDescription)")
        }
        return nil
    }
        
    
    func fetchVideoAnalytics(videoId: String, accessToken: String, startDate: String? = nil, endDate: String? = nil) async -> VideoAnalyticsModel? {
        do {
            let base = APIConstants.baseURL
            let commonParams = "ids=channel==MINE&filters=video==\(videoId)&dimensions=video"
            
            // Use the provided startDate and endDate if available, otherwise use default dates
            let finalStartDate = startDate ?? "2000-12-01"
            let finalEndDate = endDate ?? Date().formattedForYouTubeAPI()
            
            // First request: views, likes, dislikes, comments, shares
            let metrics1 = "metrics=views,likes,dislikes,comments,shares&startDate=\(finalStartDate)&endDate=\(finalEndDate)"
            let url1 = "\(base)/v2/reports?\(commonParams)&\(metrics1)"
            let response1: EngagementAnalyticsResponse = try await NetworkManager.shared.fetch(
                urlString: url1,
                accessToken: accessToken
            )
            
            // Second request: averageViewDuration, averageViewPercentage
            let metrics2 = "metrics=averageViewDuration,averageViewPercentage&startDate=\(finalStartDate)&endDate=\(finalEndDate)"
            let url2 = "\(base)/v2/reports?\(commonParams)&\(metrics2)"
            let response2: AverageDurationAndViewAnalyticsResponse = try await NetworkManager.shared.fetch(
                urlString: url2,
                accessToken: accessToken
            )
            
            // Third request: subscribersGained
            let metrics3 = "metrics=subscribersGained,subscribersLost&startDate=\(finalStartDate)&endDate=\(finalEndDate)"
            let url3 = "\(base)/v2/reports?\(commonParams)&\(metrics3)"
            let response3: SubscribersGainedAnalyticsResponse = try await NetworkManager.shared.fetch(
                urlString: url3,
                accessToken: accessToken
            )
            
            // Ensure we have valid data for all three metrics
            guard
                let row1 = response1.rows.first,
                let row2 = response2.rows.first,
                let row3 = response3.rows.first
            else {
                print("Error: Incomplete analytics data")
                return nil
            }
            
            // Extract data from response rows
            let views = row1.views
            let likes = row1.likes
            let comments = row1.comments
            let shares = row1.shares
            
            let avgDuration = row2.averageViewDuration
            let avgPercentage = row2.averageViewPercentage
            
            let subscribersGained = row3.subscribersGained
            let subscribersLost = row3.subscribersLost
            
            let engagementRate = views > 0 ? Double(likes + comments + shares) / Double(views) * 100.0 : 0.0
            
            // Create the model to return
            let model = VideoAnalyticsModel(
                videoId: videoId,
                views: views,
                likes: likes,
                comments: comments,
                shares: shares,
                averageViewDuration: avgDuration,
                averageViewPercentage: avgPercentage,
                subscribersGained: subscribersGained,
                subscribersLost: subscribersLost,
                engagementRate: engagementRate,
                retentionRate: avgPercentage
            )

            return model

        } catch let error as APIError {
            print("API Error: \(error.localizedDescription)")
        } catch {
            print("Unexpected error: \(error.localizedDescription)")
        }
        return nil
    }

    
    func fetchEngagementMetrics(videoId: String, accessToken: String,startDate: String,endDate: String) async -> EngagementAnalyticsResponse? {
        
        do {
            let base = APIConstants.baseURL
            let commonParams = "ids=channel==MINE&filters=video==\(videoId)&dimensions=video"
            let metrics = "metrics=views,likes,dislikes,comments,shares"
            let url = "\(base)/v2/reports?\(commonParams)&\(metrics)&startDate=\(startDate)&endDate=\(endDate)"
            
            let response: EngagementAnalyticsResponse = try await NetworkManager.shared.fetch(
                urlString: url,
                accessToken: accessToken
            )
            
            return response
        } catch let error as APIError {
            print("API Error: \(error.localizedDescription)")
        } catch {
            print("Unexpected error: \(error.localizedDescription)")
        }
        return nil
    }
    
    func fetchRevenueAnalytics( accessToken: String,startDate: String? = nil,endDate: String? = nil,currency: String = "USD" ) async throws -> Double? {
        let start = startDate ?? "2006-08-01"
        let end = endDate ?? Date().formattedForYouTubeAPI()
        
        let urlString = "\(APIConstants.baseURL)/v2/reports?ids=channel==MINE&startDate=\(start)&endDate=\(end)&metrics=estimatedRevenue&currency=\(currency)"
        
        do {
            let response: EstimatedRevenueAnalyticsResponse = try await NetworkManager.shared.fetch(
                urlString: urlString,
                accessToken: accessToken
            )
            
            guard let revenueRow = response.rows.first, let revenue = revenueRow.first else {
                print("Revenue data not available")
                return nil
            }
            return revenue
        } catch let error as APIError {
            throw error
        } catch {
            throw error
        }
    }
    
//    func fetchRevenueAnalytics(
//        accessToken: String,
//        startDate: String? = nil,
//        endDate: String? = nil,
//        currency: String = "INR"
//    ) async throws -> Double? {
//        // Simulate random revenue between ₹5000 and ₹20000
//        let simulatedRevenue = Double.random(in: 5000...20000)
//        print("Simulated revenue: \(simulatedRevenue)")
//        return simulatedRevenue
//    }




    func fetchUploadsPlaylistId(accessToken: String) async -> String? {
        let urlString = "\(APIConstants.youtubeBaseURL)/channels?part=contentDetails&mine=true"
        
        do {
            let response: ChannelListResponse = try await NetworkManager.shared.fetch(
                urlString: urlString,
                accessToken: accessToken
            )
            return response.items.first?.contentDetails.relatedPlaylists.uploads
            
            
            
        } catch {
            print("Error fetching uploads playlist ID: \(error)")
            return nil
        }
    }
    
    func fetchUploadedVideos(accessToken: String, playlistId: String, pageToken: String? = nil) async -> (videos: [YouTubeVideo], nextPageToken: String?)? {
        var urlString = "\(APIConstants.youtubeBaseURL)/playlistItems?part=snippet,contentDetails&playlistId=\(playlistId)&maxResults=5"
        
        if let pageToken = pageToken {
            urlString += "&pageToken=\(pageToken)"
        }
        
        var result: [YouTubeVideo] = []
        do {
            let data: UserVideosResponse = try await NetworkManager.shared.fetch(
                urlString: urlString,
                accessToken: accessToken
            )
            
            // Parse videos
            for item in data.items {
                let snippet = item.snippet
                let video = YouTubeVideo(
                    title: snippet.title,
                    description: snippet.description.isEmpty == false ? snippet.description : TextConstants.Playlists.NoDesc,
                    publishedAt: snippet.publishedAt,
                    thumbnailURL: snippet.thumbnails.standard?.url ?? snippet.thumbnails.default.url,
                    playlistId: snippet.playlistId ?? "",
                    videoId: item.contentDetails.videoId
                )
                result.append(video)
            }
            
            
            
            // Return videos and the nextPageToken
            return (result, data.nextPageToken)
            
        } catch {
            print("Error fetching uploaded videos: \(error.localizedDescription)")
            return nil
        }
    }

    
    
    func fetchTimeSeriesData(accessToken: String, startDate: String, endDate: String) async throws -> YouTubeTimeSeriesAnalyticsResponse {
         let urlString = "\(APIConstants.baseURL)/v2/reports?ids=channel==MINE&metrics=views,estimatedMinutesWatched&dimensions=day&startDate=\(startDate)&endDate=\(endDate)"
         let analytics: YouTubeTimeSeriesAnalyticsResponse = try await NetworkManager.shared.fetch(
             urlString: urlString,
             accessToken: accessToken
         )
         return analytics
     }
    
    ///fucntions is playlists
    func fetchAllPlayLists(accessToken: String,pageToken: String? = nil) async throws -> (playlists:PlaylistResponse, nextPageToken: String?) {
        var urlString = "\(APIConstants.youtubeBaseURL)/playlists?part=snippet,contentDetails&mine=true&maxResults=8"
        
        if let pageToken = pageToken {
            urlString += "&pageToken=\(pageToken)"
        }
        
        do {
            let playlists: PlaylistResponse = try await NetworkManager.shared.fetch(
                urlString: urlString,
                accessToken: accessToken
            )
            return (playlists,playlists.nextPageToken)
        } catch {
            print("Error fetching uploaded playlists: \(error.localizedDescription)")
            throw error
        }
    }
    
    func fetchPlaylistItems(playlistId: String, accessToken: String) async throws -> PlaylistItemsResponse {
        let urlString = "\(APIConstants.youtubeBaseURL)/playlistItems?part=snippet&playlistId=\(playlistId)&maxResults=50"
        
        do {
            let playlistItems: PlaylistItemsResponse = try await NetworkManager.shared.fetch(
                urlString: urlString,
                accessToken: accessToken
            )
            return playlistItems
        } catch {
            print("Error fetching playlist items: \(error.localizedDescription)")
            throw error
        }
    }

    
    // MARK-Function of comments
    
    func fetchComments(videoId: String, accessToken: String, pageToken: String? = nil) async throws -> YouTubeCommentResponse {
        var urlString = "\(APIConstants.youtubeBaseURL)/commentThreads?part=snippet,replies&videoId=\(videoId)&maxResults=10&textFormat=plainText"
            if let token = pageToken {
                urlString += "&pageToken=\(token)"
            }

            let comments: YouTubeCommentResponse = try await NetworkManager.shared.fetch(
                urlString: urlString,
                accessToken: accessToken
            )
            return comments
        }

    func uploadVideoToYoutube(accessToken:String, metadata: VideoUploadMetadata, videoData:Data) async throws{
        let url = "https://www.googleapis.com/upload/youtube/v3/videos?part=snippet,status"
        do{
          _ = try await NetworkManager.shared.uploadVideoMetadata(
                urlString: url,
                accessToken: accessToken,
                metadata: metadata,
                videoData: videoData
            )
        }
        catch let error as APIError {
            print("API Error during video upload: \(error.localizedDescription)")
            throw error
        }
        catch {
            print("Unexpected error during video upload: \(error.localizedDescription)")
            throw error
        }
    }
    
    func postCommentReply(parentId: String, replyText: String,accessToken:String) async throws -> YouTubeCommentReplyResponse{
        let urlString = "\(APIConstants.youtubeBaseURL)/comments?part=snippet"
        let body = YouTubeCommentReplyRequest(snippet: .init(parentId: parentId, textOriginal: replyText))
        do{
            let response: YouTubeCommentReplyResponse = try await NetworkManager.shared.post(
                urlString: urlString,
                accessToken: accessToken,
                body: body
            )
            return response
        }
        catch let error as APIError{
            print("YouTubeCommentService APIError: \(error.localizedDescription)")
            throw error
        }
        catch {
            print("YouTubeCommentService unknown error: \(error.localizedDescription)")
            throw error
        }
        
    }
}










