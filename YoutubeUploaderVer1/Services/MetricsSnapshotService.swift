//
//  MetricsSnapshotService.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 17/06/25.
//

import Foundation
import SwiftData

@MainActor
class MetricsSnapshotService: ObservableObject {
    static let shared = MetricsSnapshotService()
    
    private var modelContext: ModelContext?
    private let googleSignInHelper: GoogleSignInHelper
    
    @Published var isCapturingSnapshot = false
    @Published var lastSnapshotDate: Date?
    
    private init() {
        self.googleSignInHelper = GoogleSignInHelper()
        loadLastSnapshotDate()
    }
    
    func setModelContext(_ context: ModelContext) {
        self.modelContext = context
    }
    
    // MARK: - Snapshot Capture
    
    /// Captures a snapshot of all video metrics
    func captureSnapshot(for videos: [YouTubeVideo]) async {
        guard let context = modelContext else {
            print("❌ ModelContext not set")
            return
        }
        
        isCapturingSnapshot = true
        defer { isCapturingSnapshot = false }
        
        let snapshotDate = Date()
        var capturedCount = 0
        
        for video in videos {
            if let analytics = video.analytics {
                let snapshot = VideoMetricsSnapshot(from: analytics, snapshotDate: snapshotDate)
                context.insert(snapshot)
                capturedCount += 1
            }
        }
        
        do {
            try context.save()
            lastSnapshotDate = snapshotDate
            saveLastSnapshotDate()
            print("✅ Captured \(capturedCount) video snapshots")
        } catch {
            print("❌ Failed to save snapshots: \(error)")
        }
    }
    
    /// Captures snapshot for a single video
    func captureSnapshot(for video: YouTubeVideo) async {
        guard let context = modelContext,
              let analytics = video.analytics else { return }
        
        let snapshot = VideoMetricsSnapshot(from: analytics)
        context.insert(snapshot)
        
        do {
            try context.save()
            print("✅ Captured snapshot for video: \(video.title)")
        } catch {
            print("❌ Failed to save snapshot: \(error)")
        }
    }
    
    // MARK: - Delta Calculations
    
    /// Calculates delta metrics for a video
    func calculateDeltaMetrics(for video: YouTubeVideo, timeframe: DeltaTimeframe = .week) -> VideoDeltaMetrics? {
        guard let context = modelContext,
              let currentAnalytics = video.analytics else { return nil }
        
        let previousSnapshot = getPreviousSnapshot(for: video.videoId, timeframe: timeframe)
        
        return VideoDeltaMetrics(
            videoId: video.videoId,
            currentMetrics: currentAnalytics,
            previousMetrics: previousSnapshot,
            deltaTimeframe: timeframe
        )
    }
    
    /// Gets the previous snapshot for comparison
    private func getPreviousSnapshot(for videoId: String, timeframe: DeltaTimeframe) -> VideoMetricsSnapshot? {
        guard let context = modelContext else { return nil }
        
        let calendar = Calendar.current
        let now = Date()
        let comparisonDate: Date
        
        switch timeframe {
        case .week:
            comparisonDate = calendar.date(byAdding: .weekOfYear, value: -1, to: now) ?? now
        case .month:
            comparisonDate = calendar.date(byAdding: .month, value: -1, to: now) ?? now
        case .quarter:
            comparisonDate = calendar.date(byAdding: .month, value: -3, to: now) ?? now
        }
        
        let descriptor = FetchDescriptor<VideoMetricsSnapshot>(
            predicate: #Predicate<VideoMetricsSnapshot> { snapshot in
                snapshot.videoId == videoId &&
                snapshot.snapshotDate >= comparisonDate &&
                snapshot.snapshotDate <= now
            },
            sortBy: [SortDescriptor(\.snapshotDate, order: .reverse)]
        )
        
        do {
            let snapshots = try context.fetch(descriptor)
            return snapshots.first
        } catch {
            print("❌ Failed to fetch previous snapshot: \(error)")
            return nil
        }
    }
    
    // MARK: - Batch Delta Calculations
    
    /// Calculates delta metrics for multiple videos
    func calculateDeltaMetrics(for videos: [YouTubeVideo], timeframe: DeltaTimeframe = .week) -> [VideoDeltaMetrics] {
        return videos.compactMap { video in
            calculateDeltaMetrics(for: video, timeframe: timeframe)
        }
    }
    
    // MARK: - Data Management
    
    /// Checks if snapshot should be captured (daily limit)
    func shouldCaptureSnapshot() -> Bool {
        guard let lastSnapshot = lastSnapshotDate else { return true }
        
        let calendar = Calendar.current
        let now = Date()
        
        // Capture once per day
        return !calendar.isDate(lastSnapshot, inSameDayAs: now)
    }
    
    /// Cleans up old snapshots (keep last 90 days)
    func cleanupOldSnapshots() async {
        guard let context = modelContext else { return }
        
        let calendar = Calendar.current
        let cutoffDate = calendar.date(byAdding: .day, value: -90, to: Date()) ?? Date()
        
        let descriptor = FetchDescriptor<VideoMetricsSnapshot>(
            predicate: #Predicate<VideoMetricsSnapshot> { snapshot in
                snapshot.snapshotDate < cutoffDate
            }
        )
        
        do {
            let oldSnapshots = try context.fetch(descriptor)
            for snapshot in oldSnapshots {
                context.delete(snapshot)
            }
            try context.save()
            print("✅ Cleaned up \(oldSnapshots.count) old snapshots")
        } catch {
            print("❌ Failed to cleanup old snapshots: \(error)")
        }
    }
    
    // MARK: - Channel Summary
    
    /// Generates weekly channel summary
    func generateWeeklySummary() async {
        guard let context = modelContext else { return }
        
        let calendar = Calendar.current
        let now = Date()
        let weekOfYear = calendar.component(.weekOfYear, from: now)
        let year = calendar.component(.year, from: now)
        
        // Check if summary already exists
        let existingDescriptor = FetchDescriptor<ChannelMetricsSummary>(
            predicate: #Predicate<ChannelMetricsSummary> { summary in
                summary.summaryType == "weekly" &&
                summary.weekOfYear == weekOfYear &&
                summary.year == year
            }
        )
        
        do {
            let existing = try context.fetch(existingDescriptor)
            if !existing.isEmpty { return } // Already exists
            
            // Get all snapshots for this week
            let weekStart = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
            let weekEnd = calendar.dateInterval(of: .weekOfYear, for: now)?.end ?? now
            
            let snapshotsDescriptor = FetchDescriptor<VideoMetricsSnapshot>(
                predicate: #Predicate<VideoMetricsSnapshot> { snapshot in
                    snapshot.snapshotDate >= weekStart &&
                    snapshot.snapshotDate <= weekEnd
                }
            )
            
            let snapshots = try context.fetch(snapshotsDescriptor)
            
            // Calculate aggregates
            let summary = ChannelMetricsSummary(
                summaryDate: now,
                summaryType: "weekly",
                weekOfYear: weekOfYear,
                year: year,
                totalViews: snapshots.reduce(0) { $0 + $1.views },
                totalLikes: snapshots.reduce(0) { $0 + $1.likes },
                totalComments: snapshots.reduce(0) { $0 + $1.comments },
                totalShares: snapshots.reduce(0) { $0 + $1.shares },
                totalSubscribersGained: snapshots.reduce(0) { $0 + $1.subscribersGained },
                totalSubscribersLost: snapshots.reduce(0) { $0 + $1.subscribersLost },
                averageEngagementRate: snapshots.isEmpty ? 0 : snapshots.reduce(0) { $0 + $1.engagementRate } / Double(snapshots.count),
                averageRetentionRate: snapshots.isEmpty ? 0 : snapshots.reduce(0) { $0 + $1.retentionRate } / Double(snapshots.count),
                videoCount: Set(snapshots.map { $0.videoId }).count
            )
            
            context.insert(summary)
            try context.save()
            print("✅ Generated weekly summary")
            
        } catch {
            print("❌ Failed to generate weekly summary: \(error)")
        }
    }
    
    // MARK: - UserDefaults Helpers
    
    private func loadLastSnapshotDate() {
        if let timestamp = UserDefaults.standard.object(forKey: "lastSnapshotDate") as? Date {
            lastSnapshotDate = timestamp
        }
    }
    
    private func saveLastSnapshotDate() {
        if let date = lastSnapshotDate {
            UserDefaults.standard.set(date, forKey: "lastSnapshotDate")
        }
    }
}
