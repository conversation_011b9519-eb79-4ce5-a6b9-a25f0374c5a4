//
//  YoutubeUploaderVer1App.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 03/04/25.
//

import SwiftUI
import SwiftData
import Cocoa

@main
struct YoutubeUploaderVer1App: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject var googleSignInHelper = GoogleSignInHelper()
    @StateObject var videoAnalyticsViewModel = YouTubeVideoAnalyticsViewModel(googleSignInHelper: GoogleSignInHelper())
    @AppStorage("themePreference") var storedThemePreference: ThemePreference = .system
    @StateObject var startupManager = StartupManager()
    @StateObject var sharedVideoHandler = SharedVideoHandler()
    
    
    var body: some Scene {
        WindowGroup {
            RootView()
                .environmentObject(googleSignInHelper)
                .environmentObject(videoAnalyticsViewModel)
                .environmentObject(sharedVideoHandler)
                .environmentObject(startupManager)
                .preferredColorScheme(storedThemePreference.colorScheme)
                .onAppear {
                    // Run memory management demo in debug mode (disabled by default)
                    #if DEBUG && false // Change to true to enable demo
                    DispatchQueue.global(qos: .background).async {
                        MemoryManagementDemo.runDemo()
                        SystemSpecsTest.runTests()
                    }
                    #endif

                    sharedVideoHandler.checkForSharedVideo()
                }
        }
        .modelContainer(for: [VideoMetricsSnapshot.self, ChannelMetricsSummary.self]) // ProjectDraft.self,
    }
}

class AppDelegate: NSObject, NSApplicationDelegate {
    func application(_ application: NSApplication, open urls: [URL]) {
        for url in urls {
            print("Opened with URL: \(url)")
        }
    }

    func applicationDidFinishLaunching(_ notification: Notification) {
        // Add keyboard shortcut for sidebar toggle (Cmd+Shift+S)
        let mainMenu = NSApplication.shared.mainMenu
        if let viewMenu = mainMenu?.item(withTitle: "View")?.submenu {
            let toggleSidebarItem = NSMenuItem(
                title: "Toggle Sidebar",
                action: #selector(toggleSidebar),
                keyEquivalent: "s"
            )
            toggleSidebarItem.keyEquivalentModifierMask = [.command, .shift]
            toggleSidebarItem.target = self
            viewMenu.addItem(toggleSidebarItem)
        }
    }

    @objc func toggleSidebar() {
        NotificationCenter.default.post(name: NSNotification.Name("ToggleSidebar"), object: nil)
    }
}

