//
//  MetricChart.swift
//  YoutubeUploaderVer1
//
//  Created by Shashank B on 21/04/25.
//

import SwiftUI
import Charts


struct MetricChart: View {
    let title: String
    let data: [VideoMetricData]
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading) {
            Text(title)
                .font(.headline)
                .padding(.bottom, 4)

            if data.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "chart.bar.xaxis")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 40, height: 40)
                        .foregroundColor(AppColor.grayText.color)
                    
                    Text("No data available for this period")
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(AppColor.grayText.color)
                }
                .frame(maxWidth: .infinity, minHeight: 200)
                .foregroundColor(AppColor.grayText.color.opacity(0.1))
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
                )
            } else {
                Chart {
                    ForEach(data) { metric in
                        LineMark(
                            x: .value("Time", metric.label),
                            y: .value("Value", metric.value)
                        )
                        .interpolationMethod(.catmullRom)
                        .foregroundStyle(color)
                        .symbol(Circle())
                        
                        PointMark(
                            x: .value("Time", metric.label),
                            y: .value("Value", metric.value)
                        )
                        .interpolationMethod(.catmullRom)
                        .foregroundStyle(color)
                        .symbol(Circle())
                        .annotation(position:.top) {
                            Text("\(metric.value)")
                                .foregroundStyle(color)
                                .font(.caption2)
                        }
                    }
                }
                .chartYAxis {
                    AxisMarks(position: .leading)
                }
                .frame(height: 200)
            }
        }
    }
}
