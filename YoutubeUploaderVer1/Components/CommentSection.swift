//
//  CommentSection.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 29/04/25.
//

import SwiftUI

struct CommentSection: View {
    let title: String
    let count: Int
    let comments: [CommentThread]

    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section Header
            HStack(alignment: .center) {
                Rectangle()
                    .fill(AppColor.primary.color)
                    .frame(width: 4, height: 20)
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(count)")
                    .font(.subheadline)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(AppColor.primary.color.opacity(0.15))
                    .foregroundColor(AppColor.primary.color)
                    .cornerRadius(12)
            }
            
            if comments.isEmpty {
                // Empty state
                HStack {
                    Spacer()
                    Text("No \(title.lowercased()) found")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.vertical, 20)
                    Spacer()
                }
            } else {
                // Comments list
                VStack(spacing: 0) {
                    ForEach(comments) { commentThread in
                        VStack(spacing: 0) {
                            CommentThreadView(commentThread: commentThread)
                                .padding(.vertical, 12)
                            
                            if commentThread.id != comments.last?.id {
                                Divider()
                                    .padding(.leading, 54) // Aligns with avatar width + spacing
                            }
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color.black.opacity(0.03))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(AppColor.primary.color.opacity(0.2), lineWidth: 1)
        )
    }
}

struct LegendItem: View {
    let color: Color
    let label: String
    
    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)
            
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}


