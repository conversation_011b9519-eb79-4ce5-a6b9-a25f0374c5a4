//
//  MetricChartSkeleton.swift
//  YoutubeUploaderVer1
//
//  Created by Shashank B on 21/04/25.
//

import SwiftUI

struct MetricChartSkeleton: View {
    var body: some View {
        VStack(alignment: .leading) {
            // Title placeholder
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.gray.opacity(0.3))
                .frame(width: 120, height: 16)
                .padding(.bottom, 8)
            
            // Simulated chart area
            ZStack {
                // Background grid lines
                VStack(spacing: 30) {
                    ForEach(0..<5) { _ in
                        Rectangle()
                            .fill(Color.gray.opacity(0.2))
                            .frame(height: 1)
                    }
                }
                .frame(height: 200)
                
                // Simulated line path with circles
                HStack(spacing: 20) {
                    ForEach(0..<7) { _ in
                        VStack {
                            Circle()
                                .fill(Color.gray.opacity(0.4))
                                .frame(width: 8, height: 8)
                            
                            Spacer()
                        }
                    }
                }
                .padding(.horizontal, 8)
                .frame(height: 200)
            }
        }
        .padding()
        .redacted(reason: .placeholder)
    }
}

#Preview {
    MetricChartSkeleton()
}

