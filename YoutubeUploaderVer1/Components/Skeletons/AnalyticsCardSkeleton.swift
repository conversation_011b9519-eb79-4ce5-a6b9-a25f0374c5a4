//
//  AnalyticsCardSkeleton.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 17/04/25.
//

import SwiftUI

struct AnalyticsCardSkeleton: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header with icon placeholder
            HStack(alignment: .top) {
                RoundedRectangle(cornerRadius: 12)
                    .fill(AppColor.textTertiary.color.opacity(0.3))
                    .frame(width: 48, height: 48)

                Spacer()

                VStack(alignment: .trailing, spacing: 2) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(AppColor.textTertiary.color.opacity(0.3))
                        .frame(width: 40, height: 16)

                    RoundedRectangle(cornerRadius: 4)
                        .fill(AppColor.textTertiary.color.opacity(0.2))
                        .frame(width: 60, height: 12)
                }
            }
            .padding(.bottom, 20)

            // Main value section
            VStack(alignment: .leading, spacing: 4) {
                RoundedRectangle(cornerRadius: 6)
                    .fill(AppColor.textTertiary.color.opacity(0.4))
                    .frame(width: 120, height: 32)

                RoundedRectangle(cornerRadius: 4)
                    .fill(AppColor.textTertiary.color.opacity(0.3))
                    .frame(width: 80, height: 16)
            }

            Spacer()
        }
        .padding(20)
        .frame(maxWidth: .infinity, minHeight: 140, alignment: .leading)
        .background(
            ZStack {
                RoundedRectangle(cornerRadius: 16)
                    .fill(AppColor.surfaceSecondary.color)

                RoundedRectangle(cornerRadius: 16)
                    .stroke(AppColor.borderPrimary.color.opacity(0.3), lineWidth: 1)
            }
        )
        .shadow(
            color: Color.black.opacity(0.05),
            radius: 8,
            x: 0,
            y: 4
        )
        .redacted(reason: .placeholder)
    }
}

#Preview {
    AnalyticsCardSkeleton()
}
