//
//  SentimentChartSkeleton.swift
//  YoutubeUploaderVer1
//
//  Created by Shashank B on 21/04/25.
//

import SwiftUI

struct SentimentChartSkeleton: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Title placeholder
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.gray.opacity(0.3))
                .frame(width: 160, height: 20)
                .padding(.bottom, 8)

            ForEach(0..<3) { _ in
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        RoundedRectangle(cornerRadius: 2)
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 60, height: 12)

                        Spacer()

                        RoundedRectangle(cornerRadius: 2)
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 50, height: 12)
                    }

                    RoundedRectangle(cornerRadius: 5)
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 10)
                }
            }

            RoundedRectangle(cornerRadius: 4)
                .fill(Color.gray.opacity(0.3))
                .frame(width: 140, height: 14)
                .padding(.top, 12)
        }
        .padding()
        .redacted(reason: .placeholder)
    }
}


#Preview {
    SentimentChartSkeleton()
}
