//
//  CardViewSkeleton.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 17/04/25.
//

import SwiftUI

struct CardViewSkeleton: View {
    var body: some View {
        VStack(alignment:.leading){
            Rectangle()
                .fill(AppColor.darkGrayBackground.color)
                .frame(height: 180)
                .frame(maxWidth: .infinity)
            
            
            Text("Playlist title")
                .padding(12)
            Text("Playlist infor")
                .padding(12)
   
        }
        .padding(.horizontal, 8)
        .background(AppColor.darkGrayBackground.color)
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .redacted(reason: .placeholder)
    }
}

//#Preview {
//    CardViewSkeleton()
//}
