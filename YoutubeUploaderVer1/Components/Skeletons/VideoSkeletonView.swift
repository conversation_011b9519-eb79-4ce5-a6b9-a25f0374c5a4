//
//  VideoSkeletonView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 17/04/25.
//

import SwiftUI

struct VideoSkeletonView: View {
    
    var screenWidth: CGFloat {
        NSScreen.main?.frame.width ?? 1440
    }
    
    var screenHeight: CGFloat {
        NSScreen.main?.frame.height ?? 900
    }
    var body: some View {
     
        HStack(spacing: 16){
            VStack {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .aspectRatio(16/9, contentMode: .fill)
                    .frame(width: screenWidth * 0.3)
                    .cornerRadius(12)
            }
            VStack(alignment: .leading, spacing: 8){
                VStack(alignment: .leading, spacing: 2){
                    Text("Sample Video Title")
                        .font(AppFontStyle.title2.style.bold())
                        .foregroundColor(AppColor.primary.color)
                        .lineLimit(2)
                       
                    
                    Text("Published on ...")
                        .font(AppFontStyle.callout.style)
                        .foregroundColor(AppColor.grayText.color)
                }
                .padding(.horizontal)
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 4), spacing: 16) {
                    ForEach(0..<4) { _ in
                        statItem(icon: "eye.fill", color: .blue, label: "Loading", value: "...")
                       
                    }
                }
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 4), spacing: 16) {
                    ForEach(0..<4) { _ in
                        statItem(icon: "clock.fill", color: .gray, label: "Loading", value: "...")
                     
                    }
                }
            
            }
            .frame(maxWidth: .infinity)
            .background(AppColor.darkBackground.color)
            .clipShape(RoundedRectangle(cornerRadius: 20))
        }
        .padding()
        .frame(height: screenHeight * 0.25)
        .background(AppColor.darkBackground.color)
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .redacted(reason: .placeholder)
             
    }
    
    func statItem(icon: String, color: Color, label: String, value: String) -> some View {
        VStack(spacing: 6) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(AppFontStyle.title2.style) // upgraded from .title

                Text(value)
                    .foregroundColor(.white)
                    .font(AppFontStyle.title2.style) // upgraded from .title
            }
            
            Text(label)
                .foregroundColor(Color.grayTextColor)
                .font(AppFontStyle.callout.style) // upgraded from .subheadline
        }
        .frame(maxWidth: .infinity)
        .padding(6)
        .background(AppColor.youtubeRed.color.opacity(0.1))
        .clipShape(
            RoundedRectangle(cornerRadius: 12)
        )
    }
}

#Preview {
    VideoSkeletonView()
}
