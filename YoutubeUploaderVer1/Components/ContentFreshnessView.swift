//
//  ContentFreshnessView.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 05/01/25.
//

import SwiftUI

struct ContentFreshnessView: View {
    @StateObject private var analyzer = ContentFreshnessAnalyzer()
    let title: String
    let description: String
    let transcript: [(TimeInterval, TimeInterval, String)]
    let tags: [String]
    let category: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Header
            headerSection
            
            // Analysis Progress
            if analyzer.isAnalyzing {
                analysisProgressSection
            }
            
            // Results
            if let analysis = analyzer.currentAnalysis, !analyzer.isAnalyzing {
                analysisResultsSection(analysis: analysis)
            }
            
            // Analyze Button
            if !analyzer.isAnalyzing {
                analyzeButtonSection
            }
        }
        .padding(24)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(AppColor.grayText.color.opacity(0.1), lineWidth: 1)
        )
        .alert("Analysis Error", isPresented: $analyzer.showErrorAlert) {
            Button("OK") {
                analyzer.showErrorAlert = false
            }
        } message: {
            Text(analyzer.errorMessage ?? "An unknown error occurred")
        }
        .onChange(of: analyzer.isAnalyzing) { isAnalyzing in
            if isAnalyzing {
                NotificationCenter.default.post(name: NSNotification.Name("ContentFreshnessStarted"), object: nil)
            } else {
                NotificationCenter.default.post(name: NSNotification.Name("ContentFreshnessCompleted"), object: nil)
            }
        }
    }
    
    private var headerSection: some View {
        HStack {
            Image(systemName: "sparkles")
                .font(.system(size: 24))
                .foregroundColor(AppColor.youtubeRed.color)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Content Freshness Score")
                    .font(AppFontStyle.headline.style.weight(.bold))
                    .foregroundColor(AppColor.primary.color)
                
                Text("Analyze how unique and original your content is")
                    .font(AppFontStyle.subheadline.style)
                    .foregroundColor(AppColor.grayText.color)
            }
            
            Spacer()
        }
    }
    
    private var analysisProgressSection: some View {
        VStack(spacing: 16) {
            HStack {
                ProgressView()
                    .scaleEffect(0.8)
                    .progressViewStyle(CircularProgressViewStyle(tint: AppColor.youtubeRed.color))
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Analyzing Content...")
                        .font(AppFontStyle.headline.style.weight(.semibold))
                        .foregroundColor(AppColor.primary.color)
                    
                    Text(analyzer.currentStep)
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(AppColor.grayText.color)
                }
                
                Spacer()
            }
            
            ProgressView(value: analyzer.analysisProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: AppColor.youtubeRed.color))
                .scaleEffect(y: 2)
        }
        .padding(20)
        .background(AppColor.darkGrayBackground.color)
        .cornerRadius(12)
    }
    
    private func analysisResultsSection(analysis: FreshnessAnalysis) -> some View {
        VStack(alignment: .leading, spacing: 20) {
            // Overall Score
            freshnessScoreCard(analysis: analysis)
            
            // Detailed Analysis
            VStack(alignment: .leading, spacing: 16) {
                // Unique Elements
                if !analysis.uniqueElements.isEmpty {
                    analysisSection(
                        title: "Unique Elements",
                        icon: "star.fill",
                        color: .green,
                        items: analysis.uniqueElements
                    )
                }
                
                // Common Elements
                if !analysis.commonElements.isEmpty {
                    analysisSection(
                        title: "Common Elements",
                        icon: "exclamationmark.triangle.fill",
                        color: .orange,
                        items: analysis.commonElements
                    )
                }
                
                // Improvement Suggestions
                if !analysis.suggestions.isEmpty {
                    analysisSection(
                        title: "Improvement Suggestions",
                        icon: "lightbulb.fill",
                        color: AppColor.youtubeRed.color,
                        items: analysis.suggestions
                    )
                }
            }
            
            // Additional Metrics
            additionalMetricsSection(analysis: analysis)
        }
    }
    
    private func freshnessScoreCard(analysis: FreshnessAnalysis) -> some View {
        HStack(spacing: 20) {
            // Score Circle
            ZStack {
                Circle()
                    .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 8)
                    .frame(width: 80, height: 80)
                
                Circle()
                    .trim(from: 0, to: analysis.overallScore / 100)
                    .stroke(analysis.category.color, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                    .frame(width: 80, height: 80)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 1.0), value: analysis.overallScore)
                
                VStack(spacing: 2) {
                    Text("\(Int(analysis.overallScore))")
                        .font(AppFontStyle.title2.style.weight(.bold))
                        .foregroundColor(AppColor.primary.color)
                    
                    Text("/ 100")
                        .font(AppFontStyle.caption1.style)
                        .foregroundColor(AppColor.grayText.color)
                }
            }
            
            // Category Info
            VStack(alignment: .leading, spacing: 8) {
                HStack(spacing: 8) {
                    Image(systemName: analysis.category.icon)
                        .foregroundColor(analysis.category.color)
                    
                    Text(analysis.category.rawValue)
                        .font(AppFontStyle.headline.style.weight(.semibold))
                        .foregroundColor(AppColor.primary.color)
                }
                
                Text(analysis.category.description)
                    .font(AppFontStyle.subheadline.style)
                    .foregroundColor(AppColor.grayText.color)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            Spacer()
        }
        .padding(20)
        .background(AppColor.darkGrayBackground.color)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(analysis.category.color.opacity(0.3), lineWidth: 2)
        )
    }
    
    private func analysisSection(
        title: String,
        icon: String,
        color: Color,
        items: [String]
    ) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .foregroundColor(color)
                
                Text(title)
                    .font(AppFontStyle.subheadline.style.weight(.semibold))
                    .foregroundColor(AppColor.primary.color)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                ForEach(Array(items.enumerated()), id: \.offset) { index, item in
                    HStack(alignment: .top, spacing: 8) {
                        Text("•")
                            .foregroundColor(color)
                            .font(AppFontStyle.body.style.weight(.bold))
                        
                        Text(item)
                            .font(AppFontStyle.body.style)
                            .foregroundColor(AppColor.primary.color)
                            .fixedSize(horizontal: false, vertical: true)
                        
                        Spacer()
                    }
                }
            }
        }
        .padding(16)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(8)
    }
    
    private func additionalMetricsSection(analysis: FreshnessAnalysis) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Additional Insights")
                .font(AppFontStyle.subheadline.style.weight(.semibold))
                .foregroundColor(AppColor.primary.color)
            
            HStack(spacing: 20) {
                metricCard(
                    title: "Market Saturation",
                    value: "\(Int(analysis.competitorSimilarity))%",
                    subtitle: "Similarity to existing content",
                    color: analysis.competitorSimilarity > 70 ? .red : (analysis.competitorSimilarity > 40 ? .orange : .green)
                )
                
                metricCard(
                    title: "Trend Status",
                    value: analysis.trendAlignment,
                    subtitle: "Current market trend",
                    color: AppColor.youtubeRed.color
                )
            }
        }
    }
    
    private func metricCard(
        title: String,
        value: String,
        subtitle: String,
        color: Color
    ) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(AppFontStyle.caption1.style.weight(.medium))
                .foregroundColor(AppColor.grayText.color)
            
            Text(value)
                .font(AppFontStyle.headline.style.weight(.bold))
                .foregroundColor(color)
            
            Text(subtitle)
                .font(AppFontStyle.caption2.style)
                .foregroundColor(AppColor.grayText.color)
        }
        .padding(12)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(8)
    }
    
    private var analyzeButtonSection: some View {
        VStack(spacing: 12) {
            // Info message when requirements not met
            if title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                HStack(spacing: 8) {
                    Image(systemName: "info.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.blue)

                    Text("Please fill out the video title and description to enable content freshness analysis")
                        .font(AppFontStyle.caption1.style)
                        .foregroundColor(AppColor.grayText.color)
                        .fixedSize(horizontal: false, vertical: true)

                    Spacer()
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                )
            }

            Button(action: {
                analyzer.analyzeContentFreshness(
                    title: title,
                    description: description,
                    transcript: transcript,
                    tags: tags,
                    category: category
                )
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "sparkles")
                        .font(.system(size: 18))

                    Text("Analyze Content Freshness")
                        .font(AppFontStyle.headline.style.weight(.semibold))
                }
                .padding(.horizontal, 32)
                .padding(.vertical, 16)
                .frame(maxWidth: .infinity)
                .background(isButtonEnabled ? AppColor.youtubeRed.color : AppColor.grayText.color.opacity(0.3))
                .foregroundColor(isButtonEnabled ? .white : AppColor.grayText.color)
                .cornerRadius(12)
                .shadow(color: isButtonEnabled ? AppColor.youtubeRed.color.opacity(0.4) : Color.clear, radius: isButtonEnabled ? 8 : 0, x: 0, y: isButtonEnabled ? 4 : 0)
            }
            .buttonStyle(.plain)
            .disabled(!isButtonEnabled)
        }
    }

    private var isButtonEnabled: Bool {
        !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
}

#Preview {
    ContentFreshnessView(
        title: "How to Build iOS Apps with SwiftUI",
        description: "Learn the fundamentals of iOS development using SwiftUI framework",
        transcript: [],
        tags: ["iOS", "SwiftUI", "Programming"],
        category: "Education"
    )
    .padding()
    .background(AppColor.darkBackground.color)
}
