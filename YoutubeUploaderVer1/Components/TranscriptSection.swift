//
//  TranscriptSection.swift
//  YoutubeUploaderVer1
//
//  Created by Shashank B on 05/05/25.
//

import SwiftUI

struct TranscriptSection: View {
    @Binding var isVisible: Bool
    let transcriptItems: [(TimeInterval, TimeInterval, String)]
    let onExport: () -> Void
    var onRemoveRequest: (() -> Void)? = nil

    @State private var showRemoveConfirmation = false

    var body: some View {
        if isVisible, !transcriptItems.isEmpty {
            VStack(alignment: .leading, spacing: 16) {
                // Section header
                HStack {
                    Text("Video Transcript")
                        .font(AppFontStyle.headline.style.weight(.bold))
                        .foregroundColor(AppColor.primary.color)

                    Spacer()

                    Button {
                        onExport()
                    } label: {
                        HStack(spacing: 6) {
                            Image(systemName: "square.and.arrow.up")
                                .font(.system(size: 12))
                            Text("Export")
                                .font(AppFontStyle.footnote.style.weight(.medium))
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(AppColor.darkBackground.color)
                        )
                        .foregroundColor(AppColor.primary.color)
                    }
                    .buttonStyle(.plain)

                    Button {
                        if let onRemoveRequest = onRemoveRequest {
                            onRemoveRequest()
                        } else {
                            showRemoveConfirmation = true
                        }
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 20))
                            .foregroundColor(AppColor.grayText.color)
                    }
                    .buttonStyle(.plain)
                }

                // Transcript content
                ScrollView {
                    VStack(alignment: .leading, spacing: 0) {
                        ForEach(Array(transcriptItems.enumerated()), id: \.offset) { index, item in
                            let (startTime, _, text) = item
                            HStack(alignment: .top, spacing: 16) {
                                // Timestamp
                                Text(formatTimestamp(startTime))
                                    .font(AppFontStyle.footnote.style.weight(.medium))
                                    .foregroundColor(AppColor.grayText.color)
                                    .frame(width: 50, alignment: .leading)

                                // Transcript text
                                Text(text)
                                    .font(AppFontStyle.body.style)
                                    .foregroundColor(AppColor.primary.color)
                                    .fixedSize(horizontal: false, vertical: true)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                            }
                            .padding(.vertical, 8)

                            if index != transcriptItems.count - 1 {
                                Divider()
                                    .padding(.leading, 66)
                            }
                        }
                    }
                }
                .frame(height: 240)
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 24)
                    .fill(AppColor.darkGrayBackground.color.opacity(0.5))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 24)
                    .stroke(AppColor.grayText.color.opacity(0.1), lineWidth: 1)
            )
            .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
            .transition(.opacity.combined(with: .move(edge: .top)))
            .alert("Hide Transcription Section", isPresented: $showRemoveConfirmation) {
                Button("Hide Section", role: .destructive) {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isVisible = false
                    }
                }
                Button("Keep Section", role: .cancel) { }
            } message: {
                Text("Are you sure you want to hide the transcription section? You can always show it again later.")
            }
        }
    }
}


