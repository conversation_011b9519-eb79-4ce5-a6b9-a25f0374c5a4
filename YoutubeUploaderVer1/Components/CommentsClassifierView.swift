//
//  CommentsClassifierView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 15/04/25.
//

import SwiftUI


struct CommentsClassifierView: View {
    @StateObject var commentVM: CommentViewModel
    @StateObject var analysisVM = CommentClassifierViewModel()
    let localAIService = LocalAIService.shared
    init(videoId:String) {
        self.videoId = videoId
        _commentVM = StateObject(wrappedValue: CommentViewModel())
    }

    @State private var videoId: String

    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // MARK: - Sentiment Analysis Header
                VStack(spacing: 16) {
                    Text("Comment Sentiment Analysis")
                        .font(.title2)
                        .fontWeight(.bold)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    if commentVM.isLoading {
                        // Loading state
                        SentimentChartSkeleton()
                            .frame(height: 220)
                            .padding()
                            .blinking(duration: 0.75)
                    } else {
                        // Enhanced Pie Chart section
                        VStack(spacing: 16) {
                            SentimentPieChart(sentiments: [
                                SentimentData(label: "Positive", count: analysisVM.positiveCount, color: .green),
                                SentimentData(label: "Negative", count: analysisVM.negativeCount, color: .red),
                                SentimentData(label: "Neutral", count: analysisVM.neutralCount, color: AppColor.grayText.color)
                            ])

                            // Additional Stats Summary
                            HStack(spacing: 20) {
                                VStack(spacing: 4) {
                                    Text("Total Comments")
                                        .font(AppFontStyle.caption1.style)
                                        .foregroundColor(AppColor.grayText.color)
                                    Text("\(commentVM.comments.count)")
                                        .font(AppFontStyle.title3.style.weight(.bold))
                                        .foregroundColor(AppColor.primary.color)
                                }

                                Spacer()

                                if commentVM.comments.count > 0 {
                                    VStack(spacing: 4) {
                                        Text("Most Common")
                                            .font(AppFontStyle.caption1.style)
                                            .foregroundColor(AppColor.grayText.color)

                                        let mostCommon = [
                                            ("Positive", analysisVM.positiveCount),
                                            ("Negative", analysisVM.negativeCount),
                                            ("Neutral", analysisVM.neutralCount)
                                        ].max(by: { $0.1 < $1.1 })

                                        Text(mostCommon?.0 ?? "N/A")
                                            .font(AppFontStyle.title3.style.weight(.bold))
                                            .foregroundColor(mostCommon?.0 == "Positive" ? .green :
                                                           mostCommon?.0 == "Negative" ? .red : AppColor.grayText.color)
                                    }

                                    VStack(spacing: 4) {
                                        Text("Positivity Rate")
                                            .font(AppFontStyle.caption1.style)
                                            .foregroundColor(AppColor.grayText.color)

                                        let positivityRate = commentVM.comments.count > 0 ?
                                            Double(analysisVM.positiveCount) / Double(commentVM.comments.count) * 100 : 0

                                        Text("\(positivityRate, specifier: "%.1f")%")
                                            .font(AppFontStyle.title3.style.weight(.bold))
                                            .foregroundColor(positivityRate > 50 ? .green :
                                                           positivityRate > 25 ? .orange : .red)
                                    }
                                }
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(AppColor.darkGrayBackground.color.opacity(0.2))
                            )
                        }
                    }
                }
                
                if !commentVM.isLoading {
                    // MARK: - Comments Sections
                    VStack(spacing: 24) {
                        // Positive Comments Section
                        CommentSection(
                            title: "Positive Comments",
                            count: analysisVM.positiveComment.count,
                            comments: analysisVM.positiveComment
                           
                        )
                        
                        // Negative Comments Section
                        CommentSection(
                            title: "Negative Comments",
                            count: analysisVM.negativeComment.count,
                            comments: analysisVM.negativeComment
                            
                        )
                        
                        // Neutral Comments Section
                        CommentSection(
                            title: "Neutral Comments",
                            count: analysisVM.neutralComment.count,
                            comments: analysisVM.neutralComment
                            
                        )
                    }
                }
                else if commentVM.isLoading{
                    CardViewSkeleton()
                        .frame(height:300)
                        .blinking(duration: 0.75)
                }
            }
            .padding()
            .task {
                await commentVM.fetchAllComments(for: videoId)
                analysisVM.analyzeComments(commentVM.comments)
            }
        }
        .onAppear{
            Task{
                await localAIService.initializeModel()
            }
        }
    }
}



//struct CommentsClassifierView: View {
//    @StateObject var commentVM: CommentViewModel
//    @StateObject var analysisVM = CommentClassifierViewModel()
//
//    init() {
//        _commentVM = StateObject(wrappedValue: CommentViewModel(googleSignInHelper: GoogleSignInHelper()))
//    }
//
//    @State private var videoId: String = "kRp9G1NeZG8"
//    
//    var body: some View {
//        VStack(spacing: 20) {
//            if commentVM.isLoading {
//                ProgressView("Fetching comments...")
//            }
//
//            SentimentPieChart(
//                positive: Double(analysisVM.positiveCount),
//                negative: Double(analysisVM.negativeCount),
//                neutral: Double(analysisVM.neutralCount)
//            )
//
//            Text("Total Comments: \(commentVM.comments.count)")
//        }
//        .padding()
//        .task {
//            await commentVM.fetchAllComments(for: videoId)
//            analysisVM.analyzeComments(commentVM.comments)
//        }
//    }
//}
//



//struct SentimentData: Identifiable {
//    let id = UUID()
//    let sentiment: String
//    let count: Int
//    let color: Color
//}
//
//struct EnhancedSentimentChartView: View {
//    let sentiments: [SentimentData]
//    var total: Int {
//        sentiments.map { $0.count }.reduce(0, +)
//    }
//
//    var body: some View {
//        VStack(alignment: .leading, spacing: 16) {
//            Text("Sentiment Breakdown")
//                .font(.title2.bold())
//            
//            Chart {
//                ForEach(sentiments) { sentiment in
//                    SectorMark(
//                        angle: .value("Count", sentiment.count),
//                        innerRadius: .ratio(0.6),
//                        angularInset: 2.5
//                    )
//                    .foregroundStyle(sentiment.color)
//                    .annotation(position: .overlay) {
//                        if sentiment.count > 0 {
//                            Text("\(Int((Double(sentiment.count) / Double(total)) * 100))%")
//                                .font(.caption2)
//                                .foregroundColor(.white)
//                                .bold()
//                        }
//                    }
//                }
//            }
//            .frame(height: 250)
//            
//            // LEGEND
//            ForEach(sentiments) { sentiment in
//                HStack {
//                    Circle()
//                        .fill(sentiment.color)
//                        .frame(width: 12, height: 12)
//                    Text("\(sentiment.sentiment): \(sentiment.count) (\(String(format: "%.1f", (Double(sentiment.count) / Double(max(total, 1))) * 100))%)")
//                        .font(.caption)
//                        .foregroundColor(.gray)
//                }
//            }
//        }
//        .padding()
//    }
//}
