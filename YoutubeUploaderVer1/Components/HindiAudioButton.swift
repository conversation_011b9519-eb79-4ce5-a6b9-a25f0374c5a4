//
//  HindiAudioButton.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 05/01/25.
//

import SwiftUI
import AVFoundation

enum SupportedLanguage: String, CaseIterable {
    case hindi = "hi-IN"
    case english = "en-US"
    
    var displayName: String {
        switch self {
        case .hindi:
            return "Hindi"
        case .english:
            return "English"
        }
    }
    
    var iconName: String {
        switch self {
        case .hindi:
            return "globe.asia.australia"
        case .english:
            return "globe.americas"
        }
    }
}

enum TranslationStep {
    case idle
    case translatingText
    case synthesizingSpeech
    case completed
}

struct HindiAudioButton: View {
    let isTranslating: Bool
    let isPlaying: Bool
    let translationStep: TranslationStep
    let onTranslate: () -> Void
    let onStop: () -> Void
    
    var body: some View {
        Button(action: {
            if isPlaying {
                onStop()
            } else {
                onTranslate()
            }
        }) {
            HStack(spacing: 8) {
                if isTranslating {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                } else if isPlaying {
                    Image(systemName: "stop.fill")
                        .font(.system(size: 16))
                } else {
                    Image(systemName: "waveform.path")
                        .font(.system(size: 16))
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(getButtonTitle())
                        .font(AppFontStyle.body.style.weight(.semibold))
                    
                    if isTranslating {
                        Text(getProgressText())
                            .font(AppFontStyle.caption1.style)
                            .opacity(0.8)
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .frame(height: 52)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(getButtonColor())
            )
            .foregroundColor(.white)
        }
        .buttonStyle(.plain)
        .disabled(isTranslating)
        .opacity(isTranslating ? 0.8 : 1.0)
        .shadow(color: getButtonColor().opacity(0.4), radius: 6, x: 0, y: 3)
    }
    
    private func getButtonTitle() -> String {
        if isPlaying {
            return "Playing Hindi Audio"
        } else {
            return "Listen in Hindi"
        }
    }
    
    private func getButtonColor() -> Color {
        if isPlaying {
            return AppColor.grayText.color // Different color when playing
        } else {
            return AppColor.youtubeRed.color
        }
    }
    
    private func getProgressText() -> String {
        switch translationStep {
        case .idle:
            return "Starting..."
        case .translatingText:
            return "Translating to Hindi..."
        case .synthesizingSpeech:
            return "Converting to speech..."
        case .completed:
            return "Complete!"
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        // Idle state
        HindiAudioButton(
            isTranslating: false,
            isPlaying: false,
            translationStep: .idle,
            onTranslate: {},
            onStop: {}
        )
        
        // Translating state
        HindiAudioButton(
            isTranslating: true,
            isPlaying: false,
            translationStep: .translatingText,
            onTranslate: {},
            onStop: {}
        )
        
        // Playing state
        HindiAudioButton(
            isTranslating: false,
            isPlaying: true,
            translationStep: .completed,
            onTranslate: {},
            onStop: {}
        )
    }
    .padding()
    .background(AppColor.darkBackground.color)
}
