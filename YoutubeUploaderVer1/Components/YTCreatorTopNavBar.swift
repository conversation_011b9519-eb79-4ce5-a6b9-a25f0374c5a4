//
//  YTCreatorTopNavBar.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 06/04/25.
//

import SwiftUI
import AVKit

struct YTCreatorTopNavBar: View {
    @State private var searchText: String = ""
    @State private var showLogoutConfirmation = false
    @Binding var isModalPresented: Bool
    @EnvironmentObject var signInHelper: GoogleSignInHelper
    @AppStorage("themePreference") var themePreference: ThemePreference = .system
    @Environment(\.colorScheme) var currentEnvironmentScheme
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    @Binding var selectedTab: CreatorTab
    @State private var sharedVideoCount: Int = 0
    @State private var showVideoPreview = false
    @State private var sharedVideoURL: URL?
    
    
    var body: some View {
        HStack(spacing: 0) {
            // Clean Logo Section
            HStack(spacing: 12) {
                // Simple logo with YouTube red
                ZStack {
                    RoundedRectangle(cornerRadius: 10)
                        .fill(AppColor.youtubeRed.color)
                        .frame(width: 36, height: 36)

                    Image(systemName: "film.fill")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                }

                Text("YTCreator")
                    .font(.system(size: 18, weight: .bold, design: .rounded))
                    .foregroundColor(AppColor.textPrimary.color)
            }

            Spacer()

            // Clean Action Items Section
            HStack(spacing: 16) {
                // Theme Toggle Button
                Button(action: {
                    themePreference = themePreference.next()
                }) {
                    Image(systemName: {
                        switch currentEnvironmentScheme {
                        case .light: return "moon.fill"
                        case .dark: return "sun.max.fill"
                        @unknown default: return "questionmark.circle.fill"
                        }
                    }())
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
                    .frame(width: 32, height: 32)
                }
                .buttonStyle(PlainButtonStyle())
                .help("Toggle theme")

                // Shared Video Button with Badge
                ZStack(alignment: .topTrailing) {
                    Button(action: updateSharedVideoCount) {
                        Image(systemName: "tray.and.arrow.down.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(AppColor.textSecondary.color)
                            .frame(width: 32, height: 32)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .help("Check for shared video")

                    if sharedVideoCount > 0 {
                        Text("\(sharedVideoCount)")
                            .font(.system(size: 10, weight: .bold))
                            .foregroundColor(.white)
                            .frame(width: 16, height: 16)
                            .background(AppColor.youtubeRed.color)
                            .clipShape(Circle())
                            .offset(x: 6, y: -6)
                    }
                }

                // Clean Upload Button
                Button(action: {
                    selectedTab = .uploadVideos
                    navigationCoordinator.navigateToVideoUploadPage()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "arrow.up.circle.fill")
                            .font(.system(size: 14, weight: .semibold))

                        Text("Upload")
                            .font(.system(size: 14, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(AppColor.youtubeRed.color)
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
                .help("Upload a new video")
                
                //                Button(action: {
                //                    deleteGGUFFilesInDocumentsDirectory()
                //                }) {
                //                    HStack(spacing: 6) {
                //                        Image(systemName: "arrow.up")
                //
                //                            .font(AppFontStyle.headline.style)
                //
                //                        Text("Delete")
                //
                //                            .font(AppFontStyle.headline.style.weight(.medium))
                //                    }
                //                    .foregroundColor(.white)
                //                    .padding(.horizontal, 14)
                //                    .padding(.vertical, 8)
                //                    .background(AppColor.youtubeRed.color)
                //                    .cornerRadius(4)
                //                }
                //                .buttonStyle(PlainButtonStyle())

                // Clean Logout Button
                Button(action: {
                    showLogoutConfirmation = true
                }) {
                    Image(systemName: "rectangle.portrait.and.arrow.right.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                        .frame(width: 32, height: 32)
                }
                .buttonStyle(PlainButtonStyle())
                .help("Sign out")
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 12)
        .frame(height: 60)
        .background(
            ZStack {
                // Clean background
                Rectangle()
                    .fill(AppColor.surfacePrimary.color)
                    .background(.regularMaterial)

                // Simple bottom border
                VStack {
                    Spacer()
                    Rectangle()
                        .fill(AppColor.borderPrimary.color.opacity(0.2))
                        .frame(height: 1)
                        
                }
            }
        )
        .alert("Are you sure want to logout ?", isPresented: $showLogoutConfirmation){
            Button("Cancel", role: .cancel) {}
            Button("Logout", role: .destructive) {
                signInHelper.signOut()
            }
        }
        .sheet(isPresented: $showVideoPreview) {
            if let videoURL = sharedVideoURL {
                VideoPreviewSheet(videoURL: videoURL, onUploadTapped: {
//                    selectedTab = .uploadVideos
                    navigationCoordinator.navigateToVideoUploadPage(with: videoURL)
                    showVideoPreview = false
                }, isPresented: $showVideoPreview)
            }
        }
        
        .onAppear {
            updateSharedVideoCount()
        }
    }
    func deleteGGUFFilesInDocumentsDirectory() {
        let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        
        do {
            let contents = try FileManager.default.contentsOfDirectory(at: documentsURL, includingPropertiesForKeys: nil)
            
            for fileURL in contents where fileURL.pathExtension == "gguf" {
                try FileManager.default.removeItem(at: fileURL)
                print("🗑️ Deleted:", fileURL.lastPathComponent)
            }
            
        } catch {
            print("❌ Error deleting .gguf files:", error.localizedDescription)
        }
    }
    
    func updateSharedVideoCount() {
        let defaults = UserDefaults(suiteName: "group.com.codecraft.YouTube-Share")
        if let url = defaults?.url(forKey: "SharedVideoURL") {
            // Standardize the file URL to resolve any path issues
            let standardizedURL = url.standardizedFileURL
            sharedVideoCount = 1
            sharedVideoURL = standardizedURL
            showVideoPreview = true
        } else {
            sharedVideoCount = 0
        }
    }
    
}

