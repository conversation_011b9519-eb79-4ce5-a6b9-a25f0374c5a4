//
//  AnalyticsCardView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 06/04/25.
//

import SwiftUI

struct AnalyticsCardView: View {
    let title: String
    let value: String?
    let label: String
    let percentageChange: Double?
    let iconName: String

    // Dynamic accent color based on card type
    private var accentColor: Color {
        switch iconName {
        case "eye.fill":
            return AppColor.accentBlue.color
        case "clock.fill":
            return AppColor.accentOrange.color
        case "person.2.fill":
            return AppColor.accentPurple.color
        case "dollarsign.circle.fill":
            return AppColor.accentGreen.color
        default:
            return AppColor.accentBlue.color
        }
    }

    private var changeColor: Color {
        guard let change = percentageChange else { return AppColor.textTertiary.color }
        return change >= 0 ? AppColor.successGreen.color : AppColor.errorRed.color
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header with icon and trend
            HStack(alignment: .top) {
                // Icon with modern background
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                colors: [accentColor.opacity(0.15), accentColor.opacity(0.05)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 48, height: 48)

                    Image(systemName: iconName)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(accentColor)
                }

                Spacer()

                // Trend indicator
                if let change = percentageChange {
                    VStack(alignment: .trailing, spacing: 2) {
                        HStack(spacing: 4) {
                            Image(systemName: change >= 0 ? "arrow.up.right" : "arrow.down.right")
                                .font(.system(size: 12, weight: .bold))
                                .foregroundColor(changeColor)

                            Text("\(Int(abs(change)))%")
                                .font(.system(size: 14, weight: .bold))
                                .foregroundColor(changeColor)
                        }

                        Text(TextConstants.Overview.sinceWhen)
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(AppColor.textTertiary.color)
                    }
                }
            }
            .padding(.bottom, 20)

            // Main value section
            VStack(alignment: .leading, spacing: 4) {
                HStack(alignment: .firstTextBaseline, spacing: 6) {
                    Text(value ?? "N/A")
                        .font(.system(size: 28, weight: .bold, design: .rounded))
                        .foregroundColor(AppColor.textPrimary.color)

                    if !label.isEmpty {
                        Text(label)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(AppColor.textSecondary.color)
                    }
                }

                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
                    .lineLimit(2)
            }

            Spacer()
        }
        .padding(20)
        .frame(maxWidth: .infinity, minHeight: 140, alignment: .leading)
        .background(
            ZStack {
                // Main background with subtle gradient
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [
                                AppColor.surfaceSecondary.color,
                                AppColor.surfaceTertiary.color.opacity(0.8)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )

                // Subtle border
                RoundedRectangle(cornerRadius: 16)
                    .stroke(AppColor.borderPrimary.color.opacity(0.5), lineWidth: 1)

                // Accent glow effect
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        LinearGradient(
                            colors: [accentColor.opacity(0.3), accentColor.opacity(0.0)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            }
        )
        .shadow(
            color: Color.black.opacity(0.08),
            radius: 12,
            x: 0,
            y: 4
        )
        .shadow(
            color: accentColor.opacity(0.1),
            radius: 20,
            x: 0,
            y: 8
        )
    }
}

//#Preview {
//    AnalyticsCardView(title: "Views", value: "1.2M", label: "views", percentage: 2.6, iconName: "eye.fill")
//}
