//
//  GranularityDropdown.swift
//  YoutubeUploaderVer1
//
//  Created by Shashank B on 18/04/25.
//

import SwiftUI

struct GranularityDropdown: View {
    @ObservedObject var viewModel: YouTubeVideoAnalyticsViewModel
    @State private var isOpen = false

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Dropdown button
            Button {
                withAnimation { isOpen.toggle() }
            } label: {
                HStack(spacing: 120) {
                    Text(viewModel.selectedGranularity.displayName)
                        .foregroundColor(AppColor.grayText.color)
                    Image(systemName: isOpen ? "chevron.up" : "chevron.down")
                        .foregroundColor(AppColor.grayText.color)
                }
                .padding()
                .background(AppColor.darkGrayBackground.color.opacity(0.2))
                .cornerRadius(50)
            }
            .padding(.horizontal)

            // Dropdown options
            if isOpen {
                VStack(alignment: .leading, spacing: 0) {
                    ForEach(EngagementGranularity.allCases, id: \.self) { option in
                        Button {
                            viewModel.selectedGranularity = option
                            isOpen = false
                            Task {
                                await viewModel.loadEngagementGraph(videoId: "kRp9G1NeZG8")
                            }
                        } label: {
                            Text(option.displayName)
                                .foregroundColor(AppColor.grayText.color)
                                .padding()
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(AppColor.darkGrayBackground.color.opacity(0.15))
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .background(AppColor.darkGrayBackground.color.opacity(0.2))
                .cornerRadius(12)
                .padding(.horizontal)
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
    }
}


