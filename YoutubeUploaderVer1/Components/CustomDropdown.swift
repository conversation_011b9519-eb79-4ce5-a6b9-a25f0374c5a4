//
//  CustomDropdown.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 15/04/25.
//

import SwiftUI

struct CustomDropdown: View {
    @ObservedObject var viewModel:YouTubeAnalyticsViewModel
    @State private var isDropdownOpen = false
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Dropdown button
            Button(action: {
                withAnimation {
                    isDropdownOpen.toggle()
                }
            }) {
                HStack(spacing: 120) {
                    Text(viewModel.selectedTimeRange.displayName)
                        .foregroundColor(AppColor.grayText.color)
                    Image(systemName: isDropdownOpen ? "chevron.up" : "chevron.down")
                        .foregroundColor(AppColor.grayText.color)
                }
                .padding()
                .background(AppColor.darkGrayBackground.color.opacity(0.2))
                .cornerRadius(50)
            }
            .padding(.horizontal)
            
            // Dropdown options
            if isDropdownOpen {
                VStack(alignment: .leading, spacing: 0) {
                    ForEach(TimeRangeOption.allCases, id: \.self) { option in
                        Button(action: {
                            withAnimation {
                                viewModel.selectedTimeRange = option
                                isDropdownOpen = false
                            }
                            Task {
                                await viewModel.fetchViewsForSelectedRange()
                            }
                        }) {
                            Text(option.displayName)
                                .foregroundColor(AppColor.grayText.color)
                                .padding()
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(AppColor.darkGrayBackground.color.opacity(0.15))
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .background(AppColor.darkGrayBackground.color.opacity(0.2))
                .cornerRadius(12)
                .padding(.horizontal)
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
    }
}

//#Preview {
//    CustomDropdown()
//}
