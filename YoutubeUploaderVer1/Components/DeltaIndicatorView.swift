//
//  DeltaIndicatorView.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 17/06/25.
//

import SwiftUI

// MARK: - Delta Indicator View
struct DeltaIndicatorView: View {
    let delta: DeltaValue
    let timeframe: DeltaTimeframe
    let compact: Bool
    
    init(delta: DeltaValue, timeframe: DeltaTimeframe = .week, compact: Bool = true) {
        self.delta = delta
        self.timeframe = timeframe
        self.compact = compact
    }
    
    var body: some View {
        if compact {
            compactView
        } else {
            expandedView
        }
    }
    
    private var compactView: some View {
        HStack(spacing: 4) {
            Image(systemName: delta.trend.icon)
                .font(.system(size: 10, weight: .bold))
                .foregroundColor(trendColor)
            
            Text(delta.displayText)
                .font(.system(size: 11, weight: .semibold))
                .foregroundColor(trendColor)
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 2)
        .background(
            RoundedRectangle(cornerRadius: 4)
                .fill(trendColor.opacity(0.1))
        )
    }
    
    private var expandedView: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack(spacing: 6) {
                Image(systemName: delta.trend.icon)
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(trendColor)
                
                Text(delta.displayText)
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(trendColor)
                
                if abs(delta.percentage) > 0.1 {
                    Text("(\(delta.formattedPercentage))")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }
            }
            
            Text(timeframe.displayName)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(AppColor.textSecondary.color)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(trendColor.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 6)
                        .stroke(trendColor.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    private var trendColor: Color {
        switch delta.trend {
        case .up:
            return AppColor.accentGreen.color
        case .down:
            return AppColor.errorRed.color
        case .neutral:
            return AppColor.textSecondary.color
        }
    }
}

// MARK: - Enhanced Stat Item with Delta
struct StatItemWithDelta: View {
    let icon: String
    let color: Color
    let bgColor: Color
    let label: String
    let value: String
    let delta: DeltaValue?
    let timeframe: DeltaTimeframe
    
    var body: some View {
        HStack(spacing: 8) {
            // Icon with background
            RoundedRectangle(cornerRadius: 8)
                .fill(bgColor)
                .frame(width: 32, height: 32)
                .overlay(
                    Image(systemName: icon)
                        .foregroundColor(color)
                        .font(.system(size: 14, weight: .semibold))
                )
            
            VStack(alignment: .leading, spacing: 2) {
                HStack(spacing: 6) {
                    Text(value)
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(AppColor.textPrimary.color)
                    
                    if let delta = delta {
                        DeltaIndicatorView(delta: delta, timeframe: timeframe, compact: true)
                    }
                }
                
                Text(label)
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
            }
            
            Spacer()
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(AppColor.surfaceTertiary.color.opacity(0.5))
        )
    }
}

// MARK: - Delta Summary Card
struct DeltaSummaryCard: View {
    let title: String
    let deltas: [String: DeltaValue]
    let timeframe: DeltaTimeframe
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(title)
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)
                
                Spacer()
                
                Text(timeframe.displayName)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(AppColor.surfaceSecondary.color)
                    )
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                ForEach(Array(deltas.keys.sorted()), id: \.self) { key in
                    if let delta = deltas[key] {
                        deltaItem(label: key, delta: delta)
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.surfaceSecondary.color)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private func deltaItem(label: String, delta: DeltaValue) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(label.capitalized)
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(AppColor.textSecondary.color)
            
            DeltaIndicatorView(delta: delta, timeframe: timeframe, compact: false)
        }
    }
}

// MARK: - Trend Sparkline
struct TrendSparkline: View {
    let dataPoints: [Double]
    let color: Color
    let height: CGFloat
    
    init(dataPoints: [Double], color: Color = AppColor.accentBlue.color, height: CGFloat = 20) {
        self.dataPoints = dataPoints
        self.color = color
        self.height = height
    }
    
    var body: some View {
        GeometryReader { geometry in
            Path { path in
                guard dataPoints.count > 1 else { return }
                
                let maxValue = dataPoints.max() ?? 1
                let minValue = dataPoints.min() ?? 0
                let range = maxValue - minValue
                
                let stepX = geometry.size.width / CGFloat(dataPoints.count - 1)
                
                for (index, value) in dataPoints.enumerated() {
                    let x = CGFloat(index) * stepX
                    let normalizedValue = range > 0 ? (value - minValue) / range : 0.5
                    let y = geometry.size.height * (1 - normalizedValue)
                    
                    if index == 0 {
                        path.move(to: CGPoint(x: x, y: y))
                    } else {
                        path.addLine(to: CGPoint(x: x, y: y))
                    }
                }
            }
            .stroke(color, style: StrokeStyle(lineWidth: 2, lineCap: .round, lineJoin: .round))
        }
        .frame(height: height)
    }
}

// MARK: - Performance Badge
struct PerformanceBadge: View {
    let performance: PerformanceLevel
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: performance.icon)
                .font(.system(size: 10, weight: .bold))
            
            Text(performance.title)
                .font(.system(size: 10, weight: .semibold))
        }
        .foregroundColor(performance.color)
        .padding(.horizontal, 6)
        .padding(.vertical, 3)
        .background(
            RoundedRectangle(cornerRadius: 4)
                .fill(performance.color.opacity(0.1))
        )
    }
}

enum PerformanceLevel {
    case excellent, good, average, poor
    
    var title: String {
        switch self {
        case .excellent: return "Excellent"
        case .good: return "Good"
        case .average: return "Average"
        case .poor: return "Poor"
        }
    }
    
    var icon: String {
        switch self {
        case .excellent: return "star.fill"
        case .good: return "arrow.up.circle.fill"
        case .average: return "minus.circle.fill"
        case .poor: return "arrow.down.circle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .excellent: return AppColor.successGreen.color
        case .good: return AppColor.accentGreen.color
        case .average: return AppColor.accentOrange.color
        case .poor: return AppColor.errorRed.color
        }
    }
    
    static func from(deltaPercentage: Double) -> PerformanceLevel {
        if deltaPercentage >= 20 {
            return .excellent
        } else if deltaPercentage >= 5 {
            return .good
        } else if deltaPercentage >= -5 {
            return .average
        } else {
            return .poor
        }
    }
}
