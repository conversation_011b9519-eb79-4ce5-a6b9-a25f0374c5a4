//
//  OnboardingView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 30/05/25.
//


import SwiftUI

struct OnboardingView: View {
    @EnvironmentObject var startupManager: StartupManager
    @State private var selectedModels: Set<String> = []
    var onDismiss: () -> Void

    var body: some View {
        ZStack {
            AppColor.darkBackground.color.ignoresSafeArea()

            VStack(spacing: 32) {
                // Welcome Header
                VStack(spacing: 12) {
                    Text("Welcome to YoutubeUploader")
                        .font(AppFontStyle.title2.style)
                        .foregroundColor(.white)

                    Text("We need to download an AI model to enable AI services.")
                        .font(AppFontStyle.body.style)
                        .foregroundColor(AppColor.grayText.color)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 32)

                    Text("Download might consume approx. 2GB of data.")
                        .font(AppFontStyle.caption1.style)
                        .foregroundColor(AppColor.youtubeRed.color)
                        .padding(.top, 4)
                }

                // Model Selection
                VStack(alignment: .leading, spacing: 16) {
                    Text("Select a model")
                        .font(AppFontStyle.callout.style)
                        .foregroundColor(.white)
                        .padding(.leading, 8)

                    ForEach(startupManager.repositoryMap.keys.sorted(), id: \.self) { model in
                        let modelInfo = startupManager.repositoryMap[model]!

                        ModelSelectionRow(
                            model: model,
                            modelInfo: modelInfo,
                            isSelected: selectedModels.contains(model),
                            onSelect: {
                                if selectedModels.contains(model) {
                                    selectedModels.remove(model)
                                } else {
                                    selectedModels.insert(model)
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal, 40)

                // Progress or Download Button
                if startupManager.isDownloading {
                    VStack(spacing: 12) {
                        ProgressView(value: startupManager.downloadProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: AppColor.youtubeRed.color))
                            .frame(width: 260)

                        Text("Downloading model…")
                            .font(AppFontStyle.callout.style)
                            .foregroundColor(.white)

                        Text("\(Int(startupManager.downloadProgress * 100))%")
                            .font(AppFontStyle.caption2.style)
                            .foregroundColor(AppColor.grayText.color)
                    }
                    .transition(.opacity.combined(with: .move(edge: .bottom)))
                } else {
                    Button(action: {
                        guard !selectedModels.isEmpty else { return }

                        withAnimation {
                            startupManager.startDownload(models: Array(selectedModels))
                        }
                    }) {
                        Text("Download & Continue")
                            .font(AppFontStyle.headline.style)
                            .foregroundColor(AppColor.blackColor.color)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(!selectedModels.isEmpty ? AppColor.primary.color : AppColor.grayText.color.opacity(0.3))
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                            .shadow(radius: 2)
                            .padding(.horizontal, 40)
                    }
                    .disabled(selectedModels.isEmpty)
                    .transition(.opacity.combined(with: .move(edge: .top)))
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding()
        }
        .onChange(of: startupManager.isModelReady) { ready in
            if ready {
                onDismiss()
            }
        }
    }
}

struct ModelSelectionRow: View {
    let model: String
    let modelInfo: ModelInfo
    let isSelected: Bool
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            HStack(alignment: .top, spacing: 12) {
                ZStack {
                    RoundedRectangle(cornerRadius: 4)
                        .stroke(isSelected ? AppColor.primary.color : AppColor.grayText.color.opacity(0.5), lineWidth: 2)
                        .frame(width: 22, height: 22)

                    if isSelected {
                        Image(systemName: "checkmark")
                            .font(.system(size: 14, weight: .bold))
                            .foregroundColor(AppColor.primary.color)
                    }
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(model)
                        .font(AppFontStyle.body.style)
                        .foregroundColor(.white)

                    Text(modelInfo.description)
                        .font(AppFontStyle.caption1.style)
                        .foregroundColor(AppColor.grayText.color)

                    Text("~\(String(format: "%.2f", modelInfo.sizeInGB)) GB")
                        .font(AppFontStyle.caption2.style)
                        .foregroundColor(AppColor.grayText.color)

                }

                Spacer()
            }
            .padding()
            .background(AppColor.grayText.color.opacity(0.15))
            .cornerRadius(10)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

