//
//  CustomButton.swift
//  YoutubeUploaderVer1
//
//  Created by Shashank B on 23/04/25.
//

import SwiftUI

struct CustomButton: View {
    var text: String
    var action: () -> Void
    var backgroundColor: Color = AppColor.youtubeRed.color // default value

    var body: some View {
        Button(action: {
            action()
        }) {
            Text(text)
                .font(AppFontStyle.callout.style.bold())
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(backgroundColor)
                .clipShape(RoundedRectangle(cornerRadius: 10))
                .shadow(color: backgroundColor.opacity(0.4), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.top, 8)
    }
}



