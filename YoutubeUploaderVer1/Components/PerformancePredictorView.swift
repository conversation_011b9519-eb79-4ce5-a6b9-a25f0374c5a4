//
//  PerformancePredictorView.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 08/06/25.
//

import SwiftUI

struct PerformancePredictorView: View {
    let title: String
    let description: String
    let category: String
    let thumbnailDescription: String

    @StateObject private var analyzer = PerformancePredictorAnalyzer()
    @State private var showCopyAlert = false
    @State private var copiedText = ""

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            headerSection

            // Analysis Progress
            if analyzer.isAnalyzing {
                analysisProgressSection
            }

            // Results
            if let prediction = analyzer.prediction, !analyzer.isAnalyzing {
                resultsSection(prediction: prediction)
            }

            // Error Section
            if let error = analyzer.errorMessage, !analyzer.isAnalyzing {
                errorSection(error: error)
            }

            // Predict Button
            if !analyzer.isAnalyzing {
                predictButtonSection
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(AppColor.surfacePrimary.color.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
        .alert("Copied!", isPresented: $showCopyAlert) {
            Button("OK") { }
        } message: {
            Text("Performance data copied to clipboard successfully!")
        }
        .onChange(of: analyzer.isAnalyzing) { isAnalyzing in
            if isAnalyzing {
                NotificationCenter.default.post(name: NSNotification.Name("PerformancePredictionStarted"), object: nil)
            } else {
                NotificationCenter.default.post(name: NSNotification.Name("PerformancePredictionCompleted"), object: nil)
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            Image(systemName: "chart.line.uptrend.xyaxis")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(AppColor.youtubeRed.color)

            VStack(alignment: .leading, spacing: 4) {
                Text("Performance Prediction")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Text("AI-powered performance insights for your video")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
            }

            Spacer()
        }
    }
    
    // MARK: - Predict Button Section
    private var predictButtonSection: some View {
        VStack(spacing: 12) {
            // Info message when requirements not met
            if !isButtonEnabled {
                HStack(spacing: 8) {
                    Image(systemName: "info.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(AppColor.accentBlue.color)

                    Text("Please fill out the video title and description to enable performance prediction")
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                        .fixedSize(horizontal: false, vertical: true)

                    Spacer()
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(AppColor.accentBlue.color.opacity(0.1))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(AppColor.accentBlue.color.opacity(0.3), lineWidth: 1)
                )
            }

            Button(action: {
                Task {
                    await analyzer.predictPerformance(
                        title: title,
                        description: description,
                        category: category,
                        thumbnailDescription: thumbnailDescription
                    )
                }
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .font(.system(size: 18, weight: .semibold))

                    Text("Analyze Performance")
                        .font(.system(size: 16, weight: .semibold))
                }
                .padding(.horizontal, 32)
                .padding(.vertical, 16)
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        colors: isButtonEnabled ? [AppColor.youtubeRed.color, AppColor.youtubeRed.color.opacity(0.8)] : [AppColor.textSecondary.color.opacity(0.3)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .foregroundColor(isButtonEnabled ? .white : AppColor.textSecondary.color)
                .cornerRadius(12)
                .shadow(color: isButtonEnabled ? AppColor.youtubeRed.color.opacity(0.3) : Color.clear, radius: isButtonEnabled ? 8 : 0, x: 0, y: isButtonEnabled ? 4 : 0)
            }
            .buttonStyle(.plain)
            .disabled(!isButtonEnabled)
        }
    }

    // MARK: - Analysis Progress Section
    private var analysisProgressSection: some View {
        VStack(spacing: 16) {
            HStack(spacing: 12) {
                ProgressView()
                    .scaleEffect(0.8)
                    .progressViewStyle(CircularProgressViewStyle(tint: AppColor.youtubeRed.color))

                VStack(alignment: .leading, spacing: 4) {
                    Text("Analyzing Performance...")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(AppColor.textPrimary.color)

                    Text(analyzer.currentStep)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }

                Spacer()
            }

            ProgressView(value: analyzer.analysisProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: AppColor.youtubeRed.color))
                .scaleEffect(y: 1.5)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.surfaceSecondary.color)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Results Section
    private func resultsSection(prediction: PerformancePrediction) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // Performance Overview
            performanceOverview(prediction: prediction)

            // Key Metrics Grid
            keyMetricsGrid(metrics: prediction.metrics)

            // Performance Factors
            performanceFactors(factors: prediction.factors)

        }
    }
    
    // MARK: - Performance Overview
    private func performanceOverview(prediction: PerformancePrediction) -> some View {
        HStack(spacing: 20) {
            // Overall Score Circle
            VStack(spacing: 8) {
                ZStack {
                    Circle()
                        .stroke(AppColor.borderPrimary.color.opacity(0.3), lineWidth: 6)
                        .frame(width: 80, height: 80)

                    Circle()
                        .trim(from: 0, to: prediction.overallScore / 100)
                        .stroke(prediction.category.color, style: StrokeStyle(lineWidth: 6, lineCap: .round))
                        .frame(width: 80, height: 80)
                        .rotationEffect(.degrees(-90))
                        .animation(.easeInOut(duration: 1.0), value: prediction.overallScore)

                    VStack(spacing: 2) {
                        Text("\(Int(prediction.overallScore))")
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(AppColor.textPrimary.color)

                        Text("Score")
                            .font(.system(size: 11, weight: .medium))
                            .foregroundColor(AppColor.textSecondary.color)
                    }
                }

                VStack(spacing: 4) {
                    HStack(spacing: 6) {
                        Image(systemName: prediction.category.icon)
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(prediction.category.color)

                        Text(prediction.category.rawValue)
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(prediction.category.color)
                    }

                    Text("\(Int(prediction.confidence))% Confidence")
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }
            }

            // Quick Stats
            VStack(alignment: .leading, spacing: 12) {
                quickStatRow(
                    icon: "eye.fill",
                    title: "Est. Views",
                    value: prediction.metrics.estimatedViews.displayRange,
                    color: AppColor.accentBlue.color
                )

                quickStatRow(
                    icon: "heart.fill",
                    title: "Engagement",
                    value: "\(String(format: "%.1f", prediction.metrics.engagementRate))%",
                    color: AppColor.youtubeRed.color
                )

                quickStatRow(
                    icon: "flame.fill",
                    title: "Viral Potential",
                    value: "\(Int(prediction.metrics.viralPotential))/100",
                    color: AppColor.accentOrange.color
                )
            }

            Spacer()
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.surfaceSecondary.color)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private func quickStatRow(icon: String, title: String, value: String, color: Color) -> some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(color)
                .frame(width: 14)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)

                Text(value)
                    .font(.system(size: 13, weight: .semibold))
                    .foregroundColor(AppColor.textPrimary.color)
            }

            Spacer()
        }
    }
    
    // MARK: - Key Metrics Grid
    private func keyMetricsGrid(metrics: PerformanceMetrics) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Key Metrics")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(AppColor.textPrimary.color)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                metricCard(
                    title: "Retention",
                    value: "\(String(format: "%.1f", metrics.retentionRate))%",
                    icon: "clock.fill",
                    color: AppColor.accentGreen.color
                )

                metricCard(
                    title: "Click Rate",
                    value: "\(String(format: "%.1f", metrics.clickThroughRate))%",
                    icon: "hand.tap.fill",
                    color: AppColor.accentOrange.color
                )

                metricCard(
                    title: "Algorithm",
                    value: "\(Int(metrics.algorithmScore))/100",
                    icon: "cpu.fill",
                    color: AppColor.accentPurple.color
                )
            }
        }
    }
    
    private func metricCard(title: String, value: String, icon: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(color)

            Text(value)
                .font(.system(size: 15, weight: .bold))
                .foregroundColor(AppColor.textPrimary.color)

            Text(title)
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(AppColor.textSecondary.color)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .padding(.horizontal, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(AppColor.surfaceTertiary.color)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(AppColor.borderPrimary.color.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Performance Factors
    private func performanceFactors(factors: PerformanceFactors) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Performance Factors")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(AppColor.textPrimary.color)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                factorCard(title: "Title Strength", value: factors.titleStrength, color: AppColor.accentBlue.color)
                factorCard(title: "Description Quality", value: factors.descriptionQuality, color: AppColor.accentGreen.color)
                factorCard(title: "Topic Trending", value: factors.topicTrending, color: AppColor.accentPurple.color)
                factorCard(title: "Upload Timing", value: factors.uploadTiming, color: AppColor.accentOrange.color)
            }
        }
    }
    
    private func factorCard(title: String, value: Double, color: Color) -> some View {
        VStack(spacing: 8) {
            HStack {
                Text(title)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)

                Spacer()

                Text("\(Int(value))")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)
            }

            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(AppColor.borderPrimary.color.opacity(0.2))
                        .frame(height: 4)
                        .cornerRadius(2)

                    Rectangle()
                        .fill(color)
                        .frame(width: geometry.size.width * (value / 100), height: 4)
                        .cornerRadius(2)
                        .animation(.easeInOut(duration: 0.8), value: value)
                }
            }
            .frame(height: 4)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(AppColor.surfaceTertiary.color)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(AppColor.borderPrimary.color.opacity(0.1), lineWidth: 1)
                )
        )
    }

    // MARK: - Recommendations Section
//    private func recommendationsSection(recommendations: [String]) -> some View {
//        VStack(alignment: .leading, spacing: 12) {
//            HStack {
//                Text("Recommendations")
//                    .font(.system(size: 16, weight: .bold))
//                    .foregroundColor(AppColor.textPrimary.color)
//
//                Spacer()
//
//                Button(action: {
//                    copyRecommendations(recommendations)
//                }) {
//                    HStack(spacing: 4) {
//                        Image(systemName: "doc.on.doc")
//                            .font(.system(size: 12, weight: .medium))
//                        Text("Copy")
//                            .font(.system(size: 12, weight: .medium))
//                    }
//                    .foregroundColor(AppColor.accentBlue.color)
//                    .padding(.horizontal, 8)
//                    .padding(.vertical, 4)
//                    .background(AppColor.accentBlue.color.opacity(0.1))
//                    .cornerRadius(6)
//                }
//                .buttonStyle(.plain)
//            }
//
//            VStack(alignment: .leading, spacing: 8) {
//                ForEach(Array(recommendations.prefix(3).enumerated()), id: \.offset) { index, recommendation in
//                    HStack(alignment: .top, spacing: 8) {
//                        Text("\(index + 1).")
//                            .font(.system(size: 12, weight: .semibold))
//                            .foregroundColor(AppColor.youtubeRed.color)
//                            .frame(width: 16, alignment: .leading)
//
//                        Text(recommendation)
//                            .font(.system(size: 13, weight: .medium))
//                            .foregroundColor(AppColor.textSecondary.color)
//                            .fixedSize(horizontal: false, vertical: true)
//
//                        Spacer()
//                    }
//                }
//            }
//        }
//        .padding(16)
//        .background(
//            RoundedRectangle(cornerRadius: 12)
//                .fill(AppColor.surfaceSecondary.color)
//                .overlay(
//                    RoundedRectangle(cornerRadius: 12)
//                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
//                )
//        )
//    }

    // MARK: - Error Section
    private func errorSection(error: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 20))
                .foregroundColor(AppColor.warningOrange.color)

            VStack(alignment: .leading, spacing: 4) {
                Text("Analysis Failed")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(AppColor.textPrimary.color)

                Text(error)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
            }

            Spacer()
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.warningOrange.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppColor.warningOrange.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Helper Methods
    private var isButtonEnabled: Bool {
        !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    private func copyRecommendations(_ recommendations: [String]) {
        let text = recommendations.enumerated().map { index, recommendation in
            "\(index + 1). \(recommendation)"
        }.joined(separator: "\n\n")

        NSPasteboard.general.setString(text, forType: .string)
        copiedText = text
        showCopyAlert = true
    }
}
