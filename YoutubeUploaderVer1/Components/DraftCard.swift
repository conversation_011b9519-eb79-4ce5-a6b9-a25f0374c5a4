//
//  DraftCard.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 18/06/25.
//

import SwiftUI
import AVKit

struct DraftCard: View {
    let draft: ProjectDraft
    let isSelected: Bool
    let onSelect: (Bool) -> Void
    let onTap: () -> Void
    let onDelete: (() -> Void)?
    
    @State private var isHovered = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Thumbnail/Video Preview Section
            thumbnailSection
            
            // Content Section
            contentSection
            
            // Footer Section
            footerSection
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(AppColor.surfacePrimary.color)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            isSelected ? AppColor.accentBlue.color : AppColor.borderPrimary.color.opacity(0.2),
                            lineWidth: isSelected ? 2 : 1
                        )
                )
        )
        .shadow(
            color: isSelected ? AppColor.accentBlue.color.opacity(0.2) : Color.black.opacity(0.05),
            radius: isSelected ? 12 : 8,
            x: 0,
            y: isSelected ? 6 : 4
        )
        .scaleEffect(isHovered ? 1.02 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isHovered)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
        .onHover { hovering in
            isHovered = hovering
        }
        .onTapGesture {
            onTap()
        }
        .contextMenu {
            contextMenuItems
        }
    }
    
    // MARK: - Thumbnail Section
    private var thumbnailSection: some View {
        ZStack(alignment: .topTrailing) {
            // Video thumbnail or placeholder
            Group {
                if let videoURL = draft.videoURL {
                    VideoThumbnailView(videoURL: videoURL)
                } else {
                    placeholderThumbnail
                }
            }
            .frame(height: 180)
            .clipped()
            
            // Selection checkbox
            VStack {
                HStack {
                    Spacer()
                    
                    Button {
                        onSelect(!isSelected)
                    } label: {
                        Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(isSelected ? AppColor.accentBlue.color : .white)
                            .background(
                                Circle()
                                    .fill(isSelected ? .white : Color.black.opacity(0.3))
                                    .frame(width: 24, height: 24)
                            )
                    }
                    .buttonStyle(.plain)
                    .padding(12)
                }
                
                Spacer()
            }
            
            // Video duration badge (if available)
            if let duration = draft.videoDuration {
                VStack {
                    Spacer()
                    
                    HStack {
                        Spacer()
                        
                        Text(duration)
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(Color.black.opacity(0.7))
                            )
                            .padding(12)
                    }
                }
            }
        }
        .clipShape(
            UnevenRoundedRectangle(
                topLeadingRadius: 16,
                bottomLeadingRadius: 0,
                bottomTrailingRadius: 0,
                topTrailingRadius: 16
            )
        )
    }
    
    private var placeholderThumbnail: some View {
        ZStack {
            LinearGradient(
                colors: [
                    AppColor.surfaceSecondary.color,
                    AppColor.surfaceTertiary.color
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            VStack(spacing: 12) {
                Image(systemName: "film")
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
                
                Text("No Video")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
            }
        }
    }
    
    // MARK: - Content Section
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Project name
            Text(draft.projectName)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(AppColor.textPrimary.color)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            // Title (if different from project name)
            if !draft.title.isEmpty && draft.title != draft.projectName {
                Text(draft.title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
                    .lineLimit(1)
            }
            
            // Description preview
            if !draft.description.isEmpty {
                Text(draft.description)
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(AppColor.textTertiary.color)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
            }
            
            // Content summary tags
            contentSummaryTags
        }
        .padding(.horizontal, 16)
        .padding(.top, 16)
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    private var contentSummaryTags: some View {
        HStack(spacing: 8) {
            if draft.videoURL != nil {
                tagView(text: "Video", color: AppColor.accentGreen.color)
            }
            if !draft.title.isEmpty {
                tagView(text: "Title", color: AppColor.accentBlue.color)
            }
            if !draft.description.isEmpty {
                tagView(text: "Desc", color: AppColor.accentOrange.color)
            }
            if !draft.transcriptItems.isEmpty {
                tagView(text: "Transcript", color: AppColor.accentPurple.color)
            }
            if draft.enableAIEnhancement {
                tagView(text: "AI", color: AppColor.youtubeRed.color)
            }
            
            Spacer()
        }
    }
    
    private func tagView(text: String, color: Color) -> some View {
        Text(text)
            .font(.system(size: 10, weight: .semibold))
            .foregroundColor(.white)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(color)
            .cornerRadius(4)
    }
    
    // MARK: - Footer Section
    private var footerSection: some View {
        VStack(spacing: 8) {
            Divider()
                .background(AppColor.borderPrimary.color.opacity(0.2))
            
            HStack {
                // Last modified
                VStack(alignment: .leading, spacing: 2) {
                    Text("Modified")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(AppColor.textTertiary.color)
                    
                    Text(draft.formattedLastModified)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }
                
                Spacer()
                
                // Action button
                Button {
                    onTap()
                } label: {
                    HStack(spacing: 6) {
                        Image(systemName: "arrow.right.circle.fill")
                            .font(.system(size: 14, weight: .medium))
                        Text("Open")
                            .font(.system(size: 12, weight: .semibold))
                    }
                    .foregroundColor(AppColor.accentBlue.color)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(AppColor.accentBlue.color.opacity(0.1))
                    )
                }
                .buttonStyle(.plain)
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
    }
    
    // MARK: - Context Menu
    private var contextMenuItems: some View {
        Group {
            Button {
                onTap()
            } label: {
                Label("Open Draft", systemImage: "arrow.right.circle")
            }
            
            Button {
                onSelect(!isSelected)
            } label: {
                Label(isSelected ? "Deselect" : "Select", systemImage: isSelected ? "checkmark.circle" : "circle")
            }
            
            Divider()
            
            if let onDelete = onDelete {
                Button(role: .destructive) {
                    onDelete()
                } label: {
                    Label("Delete Draft", systemImage: "trash")
                }
            }
        }
    }
}

// MARK: - Video Thumbnail View
struct VideoThumbnailView: View {
    let videoURL: URL
    @State private var thumbnail: NSImage?
    
    var body: some View {
        Group {
            if let thumbnail = thumbnail {
                Image(nsImage: thumbnail)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } else {
                placeholderView
            }
        }
        .onAppear {
            generateThumbnail()
        }
    }
    
    private var placeholderView: some View {
        ZStack {
            LinearGradient(
                colors: [
                    AppColor.surfaceSecondary.color,
                    AppColor.surfaceTertiary.color
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: AppColor.textSecondary.color))
                .scaleEffect(0.8)
        }
    }
    
    private func generateThumbnail() {
        Task {
            let asset = AVAsset(url: videoURL)
            let imageGenerator = AVAssetImageGenerator(asset: asset)
            imageGenerator.appliesPreferredTrackTransform = true
            
            do {
                let cgImage = try await imageGenerator.image(at: .zero).image
                await MainActor.run {
                    self.thumbnail = NSImage(cgImage: cgImage, size: NSSize(width: cgImage.width, height: cgImage.height))
                }
            } catch {
                print("Failed to generate thumbnail: \(error)")
            }
        }
    }
}


