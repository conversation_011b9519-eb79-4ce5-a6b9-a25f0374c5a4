//
//  CustomSegmentedPicker.swift
//  YoutubeUploaderVer1
//
//  Created by Shashank B on 05/05/25.
//

import SwiftUI

struct CustomSegmentedPicker<T: Hashable>: View {
    let options: [T]
    @Binding var selected: T
    var titleForOption: (T) -> String = { "\($0)" }
    
    // Customizable colors
    var backgroundColor: Color = .black
    var selectedColor: Color = .white
    var selectedTextColor: Color = .black
    var unselectedTextColor: Color = .white

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 12)
                .fill(backgroundColor)
                .frame(height: 52)

            HStack(spacing: 0) {
                ForEach(options, id: \.self) { option in
                    Button(action: {
                        selected = option
                    }) {
                        Text(titleForOption(option))
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(selected == option ? selectedTextColor : unselectedTextColor)
                            .frame(maxWidth: .infinity)
                            .frame(height: 38)
                            .background(
                                Group {
                                    if selected == option {
                                        RoundedRectangle(cornerRadius: 10)
                                            .fill(selectedColor)
                                            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 2)
                                    } else {
                                        Color.clear
                                    }
                                }
                            )
                    }
                }
            }
            .padding(4)
        }
    }
}


