//
//  LanguageDropdownButton.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 05/01/25.
//

import SwiftUI

struct LanguageDropdownButton: View {
    @Binding var selectedLanguage: SupportedLanguage
    @Binding var isTranslating: Bool
    let onLanguageSelected: (SupportedLanguage) -> Void
    
    @State private var isDropdownOpen = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Main dropdown button
            Button(action: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isDropdownOpen.toggle()
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: selectedLanguage.iconName)
                        .font(.system(size: 16))
                    
                    Text(selectedLanguage.displayName)
                        .font(AppFontStyle.body.style.weight(.semibold))
                    
                    Image(systemName: "chevron.down")
                        .font(.system(size: 12))
                        .rotationEffect(.degrees(isDropdownOpen ? 180 : 0))
                        .animation(.easeInOut(duration: 0.2), value: isDropdownOpen)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .frame(height: 52)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(AppColor.darkGrayBackground.color)
                )
                .foregroundColor(AppColor.primary.color)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppColor.primary.color.opacity(0.2), lineWidth: 1)
                )
            }
            .buttonStyle(.plain)
            .disabled(isTranslating)
            .opacity(isTranslating ? 0.6 : 1.0)
            
            // Dropdown menu
            if isDropdownOpen {
                VStack(spacing: 0) {
                    ForEach(SupportedLanguage.allCases, id: \.self) { language in
                        Button(action: {
                            selectedLanguage = language
                            onLanguageSelected(language)
                            withAnimation(.easeInOut(duration: 0.2)) {
                                isDropdownOpen = false
                            }
                        }) {
                            HStack(spacing: 12) {
                                Image(systemName: language.iconName)
                                    .font(.system(size: 14))
                                    .foregroundColor(selectedLanguage == language ? AppColor.youtubeRed.color : AppColor.grayText.color)
                                
                                Text(language.displayName)
                                    .font(AppFontStyle.body.style.weight(.medium))
                                    .foregroundColor(selectedLanguage == language ? AppColor.youtubeRed.color : AppColor.primary.color)
                                
                                Spacer()
                                
                                if selectedLanguage == language {
                                    Image(systemName: "checkmark")
                                        .font(.system(size: 12, weight: .bold))
                                        .foregroundColor(AppColor.youtubeRed.color)
                                }
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(selectedLanguage == language ? AppColor.youtubeRed.color.opacity(0.1) : Color.clear)
                            )
                        }
                        .buttonStyle(.plain)
                        
                        if language != SupportedLanguage.allCases.last {
                            Divider()
                                .padding(.horizontal, 16)
                        }
                    }
                }
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(AppColor.darkGrayBackground.color)
                        .shadow(color: Color.black.opacity(0.2), radius: 8, x: 0, y: 4)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppColor.grayText.color.opacity(0.1), lineWidth: 1)
                )
                .padding(.top, 4)
                .transition(.opacity.combined(with: .move(edge: .top)))
                .zIndex(1)
            }
        }
        .onTapGesture {
            // Close dropdown when tapping outside
            if isDropdownOpen {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isDropdownOpen = false
                }
            }
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        LanguageDropdownButton(
            selectedLanguage: .constant(.hindi),
            isTranslating: .constant(false),
            onLanguageSelected: { _ in }
        )
    }
    .padding()
    .background(AppColor.darkBackground.color)
}
