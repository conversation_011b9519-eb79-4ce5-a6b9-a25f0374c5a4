import Foundation
import CoreML

class CoreMLManager {
    static let shared = CoreMLManager()
    private var model: MLModel?
    private var tokenizer: Tokenizer?
    
    private init() {
        loadModel()
        loadTokenizer()
    }
    
    private func loadModel() {
        do {
            let config = MLModelConfiguration()
            config.computeUnits = .all
            
            if let modelURL = Bundle.main.url(forResource: "llama-2-7b-chat", withExtension: "mlpackage") {
                let compiledModelURL = try MLModel.compileModel(at: modelURL)
                model = try MLModel(contentsOf: compiledModelURL, configuration: config)
            }
        } catch {
            print("Error loading model: \(error)")
        }
    }
    
    private func loadTokenizer() {
        guard let tokenizerURL = Bundle.main.url(forResource: "tokenizer", withExtension: "json"),
              let configURL = Bundle.main.url(forResource: "tokenizer_config", withExtension: "json") else {
            print("Failed to find tokenizer files")
            return
        }
        
        do {
            let tokenizerData = try Data(contentsOf: tokenizerURL)
            let configData = try Data(contentsOf: configURL)
            tokenizer = try Tokenizer(tokenizerData: tokenizerData, configData: configData)
        } catch {
            print("Error loading tokenizer: \(error)")
        }
    }
    
    func generateText(prompt: String) -> String? {
        guard let model = model,
              let tokenizer = tokenizer else { return nil }
        
        do {
            // Tokenize the input
            let tokens = try tokenizer.encode(text: prompt)
            
            // Create input arrays
            let inputIds = try MLMultiArray(shape: [1, 64], dataType: .int32)
            let attentionMask = try MLMultiArray(shape: [1, 64], dataType: .int32)
            
            // Fill input arrays
            for (index, token) in tokens.enumerated() {
                if index < 64 {
                    inputIds[index] = NSNumber(value: token)
                    attentionMask[index] = NSNumber(value: 1)
                }
            }
            
            // Pad remaining positions
            for index in tokens.count..<64 {
                inputIds[index] = NSNumber(value: 0)
                attentionMask[index] = NSNumber(value: 0)
            }
            
            // Create model input
            let modelInput = try MLDictionaryFeatureProvider(dictionary: [
                "input_ids": inputIds,
                "attention_mask": attentionMask
            ])
            
            // Get prediction
            let output = try model.prediction(from: modelInput)
            
            // Get logits from output
            guard let logits = output.featureValue(for: "logits")?.multiArrayValue else {
                return nil
            }
            
            // Convert logits to tokens and decode
            let decodedTokens = processLogits(logits)
            return try tokenizer.decode(tokens: decodedTokens)
            
        } catch {
            print("Error generating text: \(error)")
            return nil
        }
    }
    
    private func processLogits(_ logits: MLMultiArray) -> [Int] {
        // Get the last token's logits
        let lastTokenIndex = logits.shape[1].intValue - 1
        var probabilities: [Float] = []
        
        // Extract probabilities for the last token
        for i in 0..<logits.shape[2].intValue {
            let index = [NSNumber(value: 0), NSNumber(value: lastTokenIndex), NSNumber(value: i)]
            probabilities.append(logits[index].floatValue)
        }
        
        // Apply softmax
        let softmaxProbs = softmax(probabilities)
        
        // Get the token with highest probability
        if let maxIndex = softmaxProbs.enumerated().max(by: { $0.element < $1.element })?.offset {
            return [maxIndex]
        }
        
        return []
    }
    
    private func softmax(_ values: [Float]) -> [Float] {
        let maxValue = values.max() ?? 0
        let expValues = values.map { exp($0 - maxValue) }
        let sum = expValues.reduce(0, +)
        return expValues.map { $0 / sum }
    }
} 