import Foundation

class Tokenizer {
    private var vocab: [String: Int] = [:]
    private var config: [String: Any] = [:]
    
    init(tokenizerData: Data, configData: Data) throws {
        // Load vocabulary
        if let vocabDict = try JSONSerialization.jsonObject(with: tokenizerData) as? [String: Int] {
            vocab = vocabDict
        }
        
        // Load config
        if let configDict = try JSONSerialization.jsonObject(with: configData) as? [String: Any] {
            config = configDict
        }
    }
    
    func encode(text: String) throws -> [Int] {
        // Simple word-based tokenization
        let words = text.components(separatedBy: .whitespacesAndNewlines)
        var tokens: [Int] = []
        
        for word in words {
            if let tokenId = vocab[word] {
                tokens.append(tokenId)
            } else {
                // Handle unknown tokens
                if let unkToken = vocab["<unk>"] {
                    tokens.append(unkToken)
                }
            }
        }
        
        return tokens
    }
    
    func decode(tokens: [Int]) throws -> String {
        // Create reverse vocabulary
        let reverseVocab = Dictionary(uniqueKeysWithValues: vocab.map { ($1, $0) })
        
        // Convert tokens back to words
        let words = tokens.compactMap { reverseVocab[$0] }
        return words.joined(separator: " ")
    }
} 