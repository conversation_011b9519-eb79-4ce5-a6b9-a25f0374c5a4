import Foundation

class TextGenerationService: ObservableObject {
    @Published var generatedText: String = ""
    @Published var isGenerating: Bool = false
    @Published var error: String?
    
    func generateText(from prompt: String) {
        isGenerating = true
        error = nil
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            if let generatedText = CoreMLManager.shared.generateText(prompt: prompt) {
                DispatchQueue.main.async {
                    self?.generatedText = generatedText
                    self?.isGenerating = false
                }
            } else {
                DispatchQueue.main.async {
                    self?.error = "Failed to generate text"
                    self?.isGenerating = false
                }
            }
        }
    }
} 