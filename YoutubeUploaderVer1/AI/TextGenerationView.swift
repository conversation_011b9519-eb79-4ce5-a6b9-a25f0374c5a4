import SwiftUI

struct TextGenerationView: View {
    @StateObject private var generationService = TextGenerationService()
    @State private var prompt: String = ""
    
    var body: some View {
        VStack(spacing: 20) {
            Text("AI Text Generation")
                .font(.title)
                .fontWeight(.bold)
            
            TextEditor(text: $prompt)
                .frame(height: 100)
                .padding(4)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                )
                .padding(.horizontal)
            
            Button(action: {
                generationService.generateText(from: prompt)
            }) {
                Text("Generate")
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.blue)
                    .cornerRadius(10)
            }
            .padding(.horizontal)
            .disabled(prompt.isEmpty || generationService.isGenerating)
            
            if generationService.isGenerating {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
            }
            
            if let error = generationService.error {
                Text(error)
                    .foregroundColor(.red)
                    .padding()
            }
            
            ScrollView {
                Text(generationService.generatedText)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
            .frame(maxHeight: .infinity)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(10)
            .padding(.horizontal)
        }
        .padding()
    }
}

#Preview {
    TextGenerationView()
} 