//
//  ProjectDraft.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 18/06/25.
//

import Foundation
import SwiftData

// MARK: - Project Draft SwiftData Model
@Model
class ProjectDraft {
    var id: UUID
    var projectName: String
    var videoURL: URL?
    var videoFileName: String?
    var title: String
    var description: String
    var privacyStatus: String
    var madeForKids: Bool
    var notifySubscribers: Bool
    var enableAIEnhancement: Bool
    var expandedAIOption: String?
    var audioFileURL: URL?
    var transcriptItems: [TranscriptItem]
    var thumbnailData: Data?
    var createdAt: Date
    var lastModified: Date
    var isAudioSectionVisible: Bool
    var isTranscriptionSectionVisible: Bool
    
    init(
        projectName: String? = nil,
        videoURL: URL? = nil,
        title: String = "",
        description: String = "",
        privacyStatus: String = "Public",
        madeForKids: Bool = false,
        notifySubscribers: Bool = false,
        enableAIEnhancement: Bool = false,
        expandedAIOption: String? = nil,
        audioFileURL: URL? = nil,
        transcriptItems: [TranscriptItem] = [],
        thumbnailData: Data? = nil,
        isAudioSectionVisible: Bool = true,
        isTranscriptionSectionVisible: Bool = true
    ) {
        self.id = UUID()
        self.videoURL = videoURL
        self.videoFileName = videoURL?.lastPathComponent
        self.title = title
        self.description = description
        self.privacyStatus = privacyStatus
        self.madeForKids = madeForKids
        self.notifySubscribers = notifySubscribers
        self.enableAIEnhancement = enableAIEnhancement
        self.expandedAIOption = expandedAIOption
        self.audioFileURL = audioFileURL
        self.transcriptItems = transcriptItems
        self.thumbnailData = thumbnailData
        self.isAudioSectionVisible = isAudioSectionVisible
        self.isTranscriptionSectionVisible = isTranscriptionSectionVisible
        self.createdAt = Date()
        self.lastModified = Date()
        
        // Auto-generate project name
        self.projectName = projectName ?? generateProjectName(from: title, videoFileName: videoURL?.lastPathComponent)
    }
    
    // MARK: - Helper Methods
    
    /// Generates a project name based on title or video filename
    private func generateProjectName(from title: String, videoFileName: String?) -> String {
        if !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return title.trimmingCharacters(in: .whitespacesAndNewlines)
        } else if let fileName = videoFileName {
            // Remove file extension and return clean name
            let nameWithoutExtension = (fileName as NSString).deletingPathExtension
            return nameWithoutExtension.isEmpty ? "Untitled Project" : nameWithoutExtension
        } else {
            return "Untitled Project"
        }
    }
    
    /// Updates the last modified date
    func updateLastModified() {
        self.lastModified = Date()
    }
    
    /// Updates the project name based on current title or video filename
    func updateProjectName() {
        self.projectName = generateProjectName(from: title, videoFileName: videoFileName)
    }
    
    /// Checks if the draft has meaningful content
    var hasContent: Bool {
        return videoURL != nil ||
               !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ||
               !description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ||
               enableAIEnhancement ||
               !transcriptItems.isEmpty
    }
    
    /// Returns a formatted string for the last modified date
    var formattedLastModified: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.dateTimeStyle = .named
        return formatter.localizedString(for: lastModified, relativeTo: Date())
    }
    
    /// Returns a formatted string for the creation date
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
    
    /// Returns the video duration if available
    var videoDuration: String? {
        guard let videoURL = videoURL else { return nil }
        // This would need to be implemented to extract video duration
        // For now, return nil - can be enhanced later
        return nil
    }
    
    /// Returns a summary of the draft content
    var contentSummary: String {
        var components: [String] = []
        
        if videoURL != nil {
            components.append("Video")
        }
        if !title.isEmpty {
            components.append("Title")
        }
        if !description.isEmpty {
            components.append("Description")
        }
        if !transcriptItems.isEmpty {
            components.append("Transcript")
        }
        if enableAIEnhancement {
            components.append("AI Enhanced")
        }
        
        return components.isEmpty ? "Empty draft" : components.joined(separator: " • ")
    }
}

// MARK: - TranscriptItem for SwiftData
extension TranscriptItem: Codable {
    // TranscriptItem is already Codable from the existing implementation
}

// MARK: - Draft Management Service
@MainActor
class DraftManagementService: ObservableObject {
    static let shared = DraftManagementService()
    
    private var modelContext: ModelContext?
    @Published var drafts: [ProjectDraft] = []
    @Published var isLoading = false
    
    private init() {}
    
    func setModelContext(_ context: ModelContext) {
        self.modelContext = context
        loadDrafts()
    }
    
    // MARK: - Draft Operations
    
    /// Saves a new draft or updates an existing one
    func saveDraft(_ draft: ProjectDraft) {
        guard let context = modelContext else {
            print("❌ ModelContext not set for draft saving")
            return
        }
        
        draft.updateLastModified()
        draft.updateProjectName()
        
        // Check if this is a new draft or update
        if !drafts.contains(where: { $0.id == draft.id }) {
            context.insert(draft)
            drafts.append(draft)
        }
        
        do {
            try context.save()
            print("✅ Draft saved: \(draft.projectName)")
            loadDrafts() // Refresh the list
        } catch {
            print("❌ Failed to save draft: \(error)")
        }
    }
    
    /// Creates a draft from current upload view state
    func createDraftFromUploadState(
        videoURL: URL?,
        title: String,
        description: String,
        privacyStatus: String,
        madeForKids: Bool,
        notifySubscribers: Bool,
        enableAIEnhancement: Bool,
        expandedAIOption: String?,
        audioFileURL: URL?,
        transcriptItems: [TranscriptItem],
        isAudioSectionVisible: Bool,
        isTranscriptionSectionVisible: Bool,
        thumbnailData: Data? = nil
    ) -> ProjectDraft {
        
        let draft = ProjectDraft(
            videoURL: videoURL,
            title: title,
            description: description,
            privacyStatus: privacyStatus,
            madeForKids: madeForKids,
            notifySubscribers: notifySubscribers,
            enableAIEnhancement: enableAIEnhancement,
            expandedAIOption: expandedAIOption,
            audioFileURL: audioFileURL,
            transcriptItems: transcriptItems,
            thumbnailData: thumbnailData,
            isAudioSectionVisible: isAudioSectionVisible,
            isTranscriptionSectionVisible: isTranscriptionSectionVisible
        )
        
        saveDraft(draft)
        return draft
    }
    
    /// Loads all drafts from the database
    func loadDrafts() {
        guard let context = modelContext else {
            print("❌ ModelContext not set for loading drafts")
            return
        }
        
        isLoading = true
        
        let descriptor = FetchDescriptor<ProjectDraft>(
            sortBy: [SortDescriptor(\.lastModified, order: .reverse)]
        )
        
        do {
            drafts = try context.fetch(descriptor)
            print("✅ Loaded \(drafts.count) drafts")
        } catch {
            print("❌ Failed to load drafts: \(error)")
            drafts = []
        }
        
        isLoading = false
    }
    
    /// Deletes a draft
    func deleteDraft(_ draft: ProjectDraft) {
        guard let context = modelContext else {
            print("❌ ModelContext not set for deleting draft")
            return
        }
        
        context.delete(draft)
        
        do {
            try context.save()
            print("✅ Draft deleted: \(draft.projectName)")
            loadDrafts() // Refresh the list
        } catch {
            print("❌ Failed to delete draft: \(error)")
        }
    }
    
    /// Deletes multiple drafts
    func deleteDrafts(_ draftsToDelete: [ProjectDraft]) {
        guard let context = modelContext else {
            print("❌ ModelContext not set for deleting drafts")
            return
        }
        
        for draft in draftsToDelete {
            context.delete(draft)
        }
        
        do {
            try context.save()
            print("✅ Deleted \(draftsToDelete.count) drafts")
            loadDrafts() // Refresh the list
        } catch {
            print("❌ Failed to delete drafts: \(error)")
        }
    }
    
    /// Clears old drafts (older than specified days)
    func clearOldDrafts(olderThanDays days: Int = 30) {
        guard let context = modelContext else { return }
        
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        let oldDrafts = drafts.filter { $0.lastModified < cutoffDate }
        
        if !oldDrafts.isEmpty {
            deleteDrafts(oldDrafts)
            print("✅ Cleared \(oldDrafts.count) old drafts")
        }
    }
}
