//
//  VideoMetricsSnapshot.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 17/06/25.
//

import Foundation
import SwiftData

// MARK: - Video Metrics Snapshot Model
@Model
class VideoMetricsSnapshot {
    var videoId: String
    var snapshotDate: Date
    var views: Int
    var likes: Int
    var comments: Int
    var shares: Int
    var subscribersGained: Int
    var subscribersLost: Int
    var averageViewDuration: Int
    var averageViewPercentage: Double
    var engagementRate: Double
    var retentionRate: Double
    var weekOfYear: Int // For weekly aggregations
    var monthOfYear: Int // For monthly aggregations
    var year: Int
    
    init(
        videoId: String,
        snapshotDate: Date = Date(),
        views: Int = 0,
        likes: Int = 0,
        comments: Int = 0,
        shares: Int = 0,
        subscribersGained: Int = 0,
        subscribersLost: Int = 0,
        averageViewDuration: Int = 0,
        averageViewPercentage: Double = 0.0,
        engagementRate: Double = 0.0,
        retentionRate: Double = 0.0
    ) {
        self.videoId = videoId
        self.snapshotDate = snapshotDate
        self.views = views
        self.likes = likes
        self.comments = comments
        self.shares = shares
        self.subscribersGained = subscribersGained
        self.subscribersLost = subscribersLost
        self.averageViewDuration = averageViewDuration
        self.averageViewPercentage = averageViewPercentage
        self.engagementRate = engagementRate
        self.retentionRate = retentionRate
        
        // Calculate time-based properties
        let calendar = Calendar.current
        self.weekOfYear = calendar.component(.weekOfYear, from: snapshotDate)
        self.monthOfYear = calendar.component(.month, from: snapshotDate)
        self.year = calendar.component(.year, from: snapshotDate)
    }
    
    // Convenience initializer from VideoAnalyticsModel
    convenience init(from analytics: VideoAnalyticsModel, snapshotDate: Date = Date()) {
        self.init(
            videoId: analytics.videoId,
            snapshotDate: snapshotDate,
            views: analytics.views,
            likes: analytics.likes,
            comments: analytics.comments,
            shares: analytics.shares,
            subscribersGained: analytics.subscribersGained,
            subscribersLost: analytics.subscribersLost,
            averageViewDuration: analytics.averageViewDuration,
            averageViewPercentage: analytics.averageViewPercentage,
            engagementRate: analytics.engagementRate,
            retentionRate: analytics.retentionRate
        )
    }
}

// MARK: - Channel Metrics Summary Model
@Model
class ChannelMetricsSummary {
    var summaryDate: Date
    var summaryType: String // "weekly", "monthly"
    var weekOfYear: Int?
    var monthOfYear: Int?
    var year: Int
    var totalViews: Int
    var totalLikes: Int
    var totalComments: Int
    var totalShares: Int
    var totalSubscribersGained: Int
    var totalSubscribersLost: Int
    var averageEngagementRate: Double
    var averageRetentionRate: Double
    var videoCount: Int
    
    init(
        summaryDate: Date = Date(),
        summaryType: String,
        weekOfYear: Int? = nil,
        monthOfYear: Int? = nil,
        year: Int,
        totalViews: Int = 0,
        totalLikes: Int = 0,
        totalComments: Int = 0,
        totalShares: Int = 0,
        totalSubscribersGained: Int = 0,
        totalSubscribersLost: Int = 0,
        averageEngagementRate: Double = 0.0,
        averageRetentionRate: Double = 0.0,
        videoCount: Int = 0
    ) {
        self.summaryDate = summaryDate
        self.summaryType = summaryType
        self.weekOfYear = weekOfYear
        self.monthOfYear = monthOfYear
        self.year = year
        self.totalViews = totalViews
        self.totalLikes = totalLikes
        self.totalComments = totalComments
        self.totalShares = totalShares
        self.totalSubscribersGained = totalSubscribersGained
        self.totalSubscribersLost = totalSubscribersLost
        self.averageEngagementRate = averageEngagementRate
        self.averageRetentionRate = averageRetentionRate
        self.videoCount = videoCount
    }
}

// MARK: - Delta Calculation Models
struct VideoDeltaMetrics {
    let videoId: String
    let currentMetrics: VideoAnalyticsModel
    let previousMetrics: VideoMetricsSnapshot?
    let deltaTimeframe: DeltaTimeframe
    
    // Calculated deltas
    var viewsDelta: DeltaValue {
        calculateDelta(current: currentMetrics.views, previous: previousMetrics?.views ?? 0)
    }
    
    var likesDelta: DeltaValue {
        calculateDelta(current: currentMetrics.likes, previous: previousMetrics?.likes ?? 0)
    }
    
    var commentsDelta: DeltaValue {
        calculateDelta(current: currentMetrics.comments, previous: previousMetrics?.comments ?? 0)
    }
    
    var sharesDelta: DeltaValue {
        calculateDelta(current: currentMetrics.shares, previous: previousMetrics?.shares ?? 0)
    }
    
    var subscribersDelta: DeltaValue {
        let currentNet = currentMetrics.subscribersGained - currentMetrics.subscribersLost
        let previousNet = (previousMetrics?.subscribersGained ?? 0) - (previousMetrics?.subscribersLost ?? 0)
        return calculateDelta(current: currentNet, previous: previousNet)
    }
    
    var engagementRateDelta: DeltaValue {
        calculateDelta(current: currentMetrics.engagementRate, previous: previousMetrics?.engagementRate ?? 0.0)
    }
    
    private func calculateDelta(current: Int, previous: Int) -> DeltaValue {
        let absolute = current - previous
        let percentage = previous > 0 ? (Double(absolute) / Double(previous)) * 100 : 0
        return DeltaValue(absolute: Double(absolute), percentage: percentage, trend: absolute > 0 ? .up : absolute < 0 ? .down : .neutral)
    }
    
    private func calculateDelta(current: Double, previous: Double) -> DeltaValue {
        let absolute = current - previous
        let percentage = previous > 0 ? (absolute / previous) * 100 : 0
        return DeltaValue(absolute: absolute, percentage: percentage, trend: absolute > 0 ? .up : absolute < 0 ? .down : .neutral)
    }
}

struct DeltaValue {
    let absolute: Double
    let percentage: Double
    let trend: TrendDirection
    
    var formattedAbsolute: String {
        if abs(absolute) >= 1000 {
            return String(format: "%.1fK", absolute / 1000)
        } else {
            return String(format: "%.0f", absolute)
        }
    }
    
    var formattedPercentage: String {
        return String(format: "%.1f%%", percentage)
    }
    
    var displayText: String {
        let sign = absolute >= 0 ? "+" : ""
        return "\(sign)\(formattedAbsolute)"
    }
}

enum DeltaTimeframe: String, CaseIterable {
    case week = "week"
    case month = "month"
    case quarter = "quarter"
    
    var displayName: String {
        switch self {
        case .week: return "vs last week"
        case .month: return "vs last month"
        case .quarter: return "vs last quarter"
        }
    }
}

enum TrendDirection {
    case up, down, neutral
    
    var icon: String {
        switch self {
        case .up: return "arrow.up"
        case .down: return "arrow.down"
        case .neutral: return "minus"
        }
    }
    
    var color: String {
        switch self {
        case .up: return "AccentGreen"
        case .down: return "ErrorRed"
        case .neutral: return "GrayText"
        }
    }
}
