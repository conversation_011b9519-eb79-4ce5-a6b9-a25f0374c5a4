//
//  Color.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 07/04/25.
//

import Foundation
import SwiftUI

extension Color {
    // Legacy colors (keeping for compatibility)
    static let redColor = Color("YoutubeRed")
    static let darkGrayColor = Color("DarkGrayBackground")
    static let darkBackgroundColor = Color("DarkBackground")
    static let lighBackgroundColor = Color("LightGrayBackground")
    static let grayTextColor = Color("GrayText")
    static let iconBackgroundColor = Color("IconBackground")
    static let primary = Color("MainColor")
    static let iconColor = Color("IconColor")

    // Gradient helpers
    static func modernGradient(from startColor: Color, to endColor: Color) -> LinearGradient {
        LinearGradient(
            colors: [startColor, endColor],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    static var backgroundGradient: LinearGradient {
        LinearGradient(
            colors: [
                Color("BackgroundGradientStart"),
                Color("BackgroundGradientEnd")
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
}

