//
//  NetworkManager.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 07/04/25.
//

import Foundation

final class NetworkManager {
    static let shared = NetworkManager()
    private init() {}
 
    func fetch<T: Decodable>(urlString: String, accessToken: String) async throws -> T {
        guard let url = URL(string: urlString) else {
            throw APIError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")

        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.noData
            }

            switch httpResponse.statusCode {
            case 200:
                do {
                    return try JSONDecoder().decode(T.self, from: data)
                } catch {
                    throw APIError.decodingError(error)
                }
            case 401:
                throw APIError.unauthorized
            default:
                throw APIError.requestFailed(statusCode: httpResponse.statusCode)
            }
        } catch {
            throw APIError.unknown(error)
        }
    }
    func uploadVideoMetadata<T: Codable>(
        urlString: String,
        accessToken: String,
        metadata: T,
        videoData: Data,
        mimeType: String = "video/*"
    ) async throws -> Data {
        guard let url = URL(string: urlString) else {
            throw APIError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json; charset=UTF-8", forHTTPHeaderField: "Content-Type")

        let metadataData = try JSONEncoder().encode(metadata)

        // For resumable uploads, you initiate with metadata and then upload video
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("multipart/related; boundary=boundary", forHTTPHeaderField: "Content-Type")

        var body = Data()

        body.append("--boundary\r\n".data(using: .utf8)!)
        body.append("Content-Type: application/json; charset=UTF-8\r\n\r\n".data(using: .utf8)!)
        body.append(metadataData)
        body.append("\r\n--boundary\r\n".data(using: .utf8)!)
        body.append("Content-Type: \(mimeType)\r\n\r\n".data(using: .utf8)!)
        body.append(videoData)
        body.append("\r\n--boundary--\r\n".data(using: .utf8)!)

        request.httpBody = body
    

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
            throw APIError.requestFailed(statusCode: (response as? HTTPURLResponse)?.statusCode ?? 0)
        }

        return data
    }
    
    func post<T: Codable, U: Decodable>(
        urlString: String,
        accessToken: String,
        body: T
    ) async throws -> U {
        guard let url = URL(string: urlString) else {
            throw APIError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            request.httpBody = try JSONEncoder().encode(body)

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.noData
            }

            switch httpResponse.statusCode {
            case 200...299:
                return try JSONDecoder().decode(U.self, from: data)
            case 401:
                throw APIError.unauthorized
            default:
                let _ = String(data: data, encoding: .utf8) ?? "Unknown error"
                throw APIError.requestFailed(statusCode: httpResponse.statusCode)
            }

        } catch {
            throw APIError.unknown(error)
        }
    }


}
