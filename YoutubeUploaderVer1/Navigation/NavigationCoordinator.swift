//
//  PlaylistRouterView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 23/04/25.
//


import SwiftUI
import Foundation
import GoogleSignIn

// MARK: - Enum to handle all Navigation Routes
enum NavigationRoute: Hashable {
    case playlistDetailView
    case videoUploadView(videoUrl: URL?)
    case videoAnalyticsView(video: YouTubeVideo)
    case playlistVideoAnalyticsView(video: YouTubeVideo)
}

// MARK: - Coordinator for navigation logic
class NavigationCoordinator: ObservableObject {
    @Published var path = NavigationPath()

    func navigateToPlaylistDetailView() {
        path.append(NavigationRoute.playlistDetailView)
    }

    func goBackOneStep() {
        if !path.isEmpty {
            path.removeLast()
        }
    }

    func navigateToViewAnalytics(video: YouTubeVideo) {
        path.append(NavigationRoute.videoAnalyticsView(video: video))
    }

    func navigateToPlaylistVideoAnalyticsView(video: YouTubeVideo) {
        path.append(NavigationRoute.playlistVideoAnalyticsView(video: video))
    }
    
    func navigateToVideoUploadPage(with url:URL? = nil){
        path.append(NavigationRoute.videoUploadView(videoUrl: url))
    }

    func navigateToVideoUploadPage(with draft: Any) { // ProjectDraft
        // For now, we'll use the main tab navigation since the NavigationRoute doesn't support drafts yet
        // This could be enhanced later to support draft-specific navigation
        NotificationCenter.default.post(
            name: NSNotification.Name("NavigateToUploadWithDraft"),
            object: draft
        )
    }
    
    func navigateToNewTab() {
        path.removeLast(path.count)
    }
//    func navigateToUploadVideoView(){
//        path.append(NavigationRoute.videosUploadView)
//    }
}

// MARK: - PlaylistRouterView as View
struct RootView: View {
    @StateObject private var navigationCoordinator = NavigationCoordinator()
    //@StateObject var signInHelper = GoogleSignInHelper()
    @EnvironmentObject var sharedVideoHandler: SharedVideoHandler
    @EnvironmentObject var googleSignInHelper: GoogleSignInHelper
    
    var isSignedIn: Bool {
        return googleSignInHelper.user != nil
    }
    
    var body: some View {
        Group {
            if let user = googleSignInHelper.user {
                DashboardView(signedInUser: user)
                    
            } else {
                SignInView()
            }
        }
        .environmentObject(googleSignInHelper)
        .environmentObject(navigationCoordinator)
    }
}
