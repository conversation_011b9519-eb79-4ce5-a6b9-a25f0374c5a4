<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.786699238565-kosccbsuu5jva6lt4kv3ajsbern4n276</string>
			</array>
		</dict>
        <dict>
            <key>CFBundleTypeRole</key>
            <string>Editor</string>
            <key>CFBundleURLName</key>
            <string>com.codecraft.YouTube-Share</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>youtube-uploader</string>
            </array>
        </dict>
	</array>
	<key>GIDClientID</key>
	<string>786699238565-kosccbsuu5jva6lt4kv3ajsbern4n276.apps.googleusercontent.com</string>
	<key>UIAppFonts</key>
	<array>
		<string>Poppins-Regular.ttf</string>
		<string>Sora-Bold.ttf</string>
	</array>
    <key>NSSpeechRecognitionUsageDescription</key>
    <string>We need access to speech recognition to transcribe audio.</string>
</dict>
</plist>
