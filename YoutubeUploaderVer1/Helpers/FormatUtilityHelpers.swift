//
//  FormatUtilityHelpers.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 21/04/25.
//

import Foundation

// Global function to format numbers (K, M)
func formatNumber(_ number: Int) -> String {
    if number >= 1_000_000_000 {
        return String(format: "%.1fB", Double(number) / 1_000_000_000)
    } else if number >= 1_000_000 {
        return String(format: "%.1fM", Double(number) / 1_000_000)
    } else if number >= 1_000 {
        return String(format: "%.1fK", Double(number) / 1_000)
    } else {
        return "\(number)"
    }
}


/// Converts an ISO 8601 date string to a human-readable "time ago" format
///
/// Takes a date string in ISO 8601 format and returns a user-friendly relative time
/// description (e.g., "2 days ago", "1 week ago", "Yesterday").
///
/// - Parameter dateString: ISO 8601 formatted date string
/// - Returns: Human-readable relative time string, or "Invalid date" if parsing fails
/// - Example: "2023-12-01T10:30:00Z" → "2 weeks ago"
func formattedTimeAgo(from dateString: String) -> String {
    let isoFormatter = ISO8601DateFormatter()
    isoFormatter.formatOptions = [.withInternetDateTime]
    
    if let date = isoFormatter.date(from: dateString) {
        return timeAgo(from: date)
    } else {
        return "Invalid date"
    }
}

/// Calculates a human-readable "time ago" string from a Date object
///
/// Converts a Date into a relative time description with appropriate granularity.
/// Uses the most significant time unit (years > months > weeks > days).
///
/// - Parameter date: The date to calculate relative time from
/// - Returns: Formatted relative time string (e.g., "3 days ago", "Yesterday", "Today")
/// - Note: Uses current date as reference point for calculations
private func timeAgo(from date: Date) -> String {
    let calendar = Calendar.current
    let now = Date()
    
    // Check if the date is within the last 24 hours
    if calendar.isDateInYesterday(date) {
        return "Yesterday"
    }
    
    let components = calendar.dateComponents([.day, .weekOfYear, .month, .year], from: date, to: now)

    if let year = components.year, year > 0 {
        return year == 1 ? "1 year ago" : "\(year) years ago"
    } else if let month = components.month, month > 0 {
        return month == 1 ? "1 month ago" : "\(month) months ago"
    } else if let week = components.weekOfYear, week > 0 {
        return week == 1 ? "1 week ago" : "\(week) weeks ago"
    } else if let day = components.day, day > 0 {
        return day == 1 ? "1 day ago" : "\(day) days ago"
    } else {
        return "Today"
    }
}

/// Calculates the percentage change between two integer values
///
/// Computes the percentage change from a previous value to a current value.
/// Handles nil values and division by zero gracefully.
///
/// - Parameters:
///   - current: The current value (optional)
///   - previous: The previous value for comparison (optional)
/// - Returns: Percentage change as Double, or nil if calculation is not possible
/// - Note: Returns nil if either value is nil or if previous value is 0 (to avoid division by zero)
/// - Example: calculatePercentageChange(current: 120, previous: 100) returns 20.0 (20% increase)
func calculatePercentageChange(current: Int?, previous: Int?) -> Double? {
    guard let current = current, let previous = previous, previous != 0 else {
        return nil
    }
    return Double(current - previous) / Double(previous) * 100
}

/// Formats a time duration in seconds to MM:SS format
///
/// Converts a duration in seconds to a human-readable timestamp format
/// suitable for video timestamps and duration displays.
///
/// - Parameter seconds: Duration in seconds as Double
/// - Returns: Formatted string in "M:SS" or "MM:SS" format
/// - Example: formatTimestamp(125.5) returns "2:05"
/// - Note: Rounds down to nearest second for display
func formatTimestamp(_ seconds: Double) -> String {
    let minutes = Int(seconds) / 60
    let seconds = Int(seconds) % 60
    return String(format: "%d:%02d", minutes, seconds)
}

/// Extracts and parses PotentialSegment objects from a fenced JSON string
///
/// This function handles JSON strings that may be wrapped in markdown code fences
/// (```json...```). It cleans the input, removes fencing, and attempts to decode
/// the JSON into an array of PotentialSegment objects.
///
/// - Parameter fencedJSONString: JSON string that may be wrapped in markdown fences
/// - Returns: Array of PotentialSegment objects, empty array if parsing fails
/// - Note: Handles both fenced and unfenced JSON strings
/// - Warning: Logs parsing errors to console for debugging
/// - Example: Input "```json\n[{...}]\n```" is cleaned to "[{...}]" before parsing
func extractPotentialSegments(from fencedJSONString: String?) -> [PotentialSegment] {
    guard var jsonString = fencedJSONString else {
        print("Error: input is nil")
        return []
    }
    
    let prefix = "```json\n"
    let suffix = "\n```"
    
    if jsonString.hasPrefix(prefix) {
        jsonString.removeFirst(prefix.count)
    }
    if jsonString.hasSuffix(suffix) {
        jsonString.removeLast(suffix.count)
    }
    
    jsonString = jsonString.trimmingCharacters(in: .whitespacesAndNewlines)
    
    guard let jsonData = jsonString.data(using: .utf8) else {
        print("Error: Could not convert cleaned JSON string to Data.")
        return []
    }
    
    do {
        let segments = try JSONDecoder().decode([PotentialSegment].self, from: jsonData)
        return segments
    } catch {
        print("Error decoding JSON: \(error)")
        print("Failed JSON: \(jsonString)")
        return []
    }
}
