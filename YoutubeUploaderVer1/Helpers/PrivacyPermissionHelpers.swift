//
//  PrivacyPermissionHelpers.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 05/05/25.
//

import Foundation
import Speech
import AppKit

/// Checks and requests speech recognition permission with comprehensive validation
///
/// This function not only checks for speech recognition authorization but also validates
/// that the speech recognition service is actually available (Dictation or Siri enabled).
/// It provides user-friendly guidance for enabling required system features.
///
/// - Parameter completion: Callback with boolean indicating if speech recognition is fully available
/// - Note: Completion is called on main queue for UI updates
/// - Warning: Even with authorization, speech recognition may be unavailable if Dictation/Siri is disabled
/// - Important: App restart may be required after enabling Dictation/Siri for changes to take effect
func checkAndRequestSpeechPermission(completion: @escaping (Bool) -> Void) {
    let status = SFSpeechRecognizer.authorizationStatus()

    switch status {
    case .authorized:
        // Also check if Dictation/Siri is enabled by checking recognizer availability
        if let recognizer = SFSpeechRecognizer(locale: Locale.current), recognizer.isAvailable {
            print("✅ Speech recognition authorized and available")
            completion(true)
        } else {
            print("❌ Speech recognition authorized but unavailable (Dictation/Siri likely disabled)")
            showEnableDictationSiriInstructions()
            completion(false)
        }

    case .notDetermined:
        SFSpeechRecognizer.requestAuthorization { newStatus in
            DispatchQueue.main.async {
                if newStatus == .authorized {
                    if let recognizer = SFSpeechRecognizer(locale: Locale.current), recognizer.isAvailable {
                        print("✅ Granted and available after request")
                        completion(true)
                    } else {
                        print("❌ Granted but unavailable (Dictation/Siri likely disabled)")
                        showEnableDictationSiriInstructions()
                        completion(false)
                    }
                } else {
                    print("❌ Denied after request")
                    showPermissionInstructions()
                    completion(false)
                }
            }
        }

    case .denied, .restricted:
        print("❌ Permission denied or restricted")
        showPermissionInstructions()
        completion(false)

    @unknown default:
        print("❌ Unknown permission status")
        completion(false)
    }
}

/// Displays instructions for enabling Dictation or Siri when speech recognition is authorized but unavailable
///
/// This function shows a detailed alert with step-by-step instructions for enabling the required
/// system features. It provides direct links to the appropriate system settings panels.
///
/// - Note: Called when speech recognition permission is granted but the service is unavailable
/// - Important: Emphasizes that app restart is required after enabling Dictation/Siri
private func showEnableDictationSiriInstructions() {
    let alert = NSAlert()
    alert.messageText = "Enable Dictation or Siri"
    alert.informativeText = """
    Speech recognition requires Dictation or Siri to be enabled on your Mac.

    ➤ To enable Dictation:
    System Settings → Keyboard → Dictation → Enable Dictation

    ➤ Or to enable Siri:
    System Settings → Siri → Enable Ask Siri

    ⚠️ IMPORTANT: After enabling, you MUST restart this app for speech recognition to work.
    """
    alert.alertStyle = .warning
    alert.addButton(withTitle: "Open Keyboard Settings")
    alert.addButton(withTitle: "Open Siri Settings")
    alert.addButton(withTitle: "Cancel")

    let response = alert.runModal()
    switch response {
    case .alertFirstButtonReturn:
        if let url = URL(string: "x-apple.systempreferences:com.apple.preference.keyboard?Keyboard") {
            NSWorkspace.shared.open(url)
        }
    case .alertSecondButtonReturn:
        if let url = URL(string: "x-apple.systempreferences:com.apple.preference.siri") {
            NSWorkspace.shared.open(url)
        }
    default:
        break
    }
}

/// Displays instructions for manually enabling speech recognition permission
///
/// This function is called when the user has previously denied speech recognition permission
/// and needs to manually enable it through System Settings. Provides direct navigation
/// to the Privacy & Security settings.
///
/// - Note: Called when permission status is .denied or .restricted
/// - Important: Provides direct link to Speech Recognition privacy settings
private func showPermissionInstructions() {
    let alert = NSAlert()
    alert.messageText = "Speech Recognition Permission Required"
    alert.informativeText = """
    You have previously denied access to Speech Recognition. Please enable it manually:

    System Settings → Privacy & Security → Speech Recognition → Enable for this app.
    """
    alert.alertStyle = .warning
    alert.addButton(withTitle: "Open Settings")
    alert.addButton(withTitle: "Cancel")
    
    let response = alert.runModal()
    if response == .alertFirstButtonReturn {
        if let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_SpeechRecognition") {
            NSWorkspace.shared.open(url)
        }
    }
}

