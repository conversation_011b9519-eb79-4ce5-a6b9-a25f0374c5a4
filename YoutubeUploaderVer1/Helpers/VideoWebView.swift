//
//  VideoWebView.swift
//  YoutubeUploaderVer1
//
//  Created by Shashan<PERSON> B on 09/04/25.
//

import SwiftUI
import WebKit

struct VideoWebView: NSViewRepresentable { // Use `UIViewRepresentable` for iOS
    let videoID: String

    func makeNSView(context: Context) -> WKWebView {
        return WKWebView()
    }

    func updateNSView(_ nsView: WKWebView, context: Context) {
        let embedURLString = "https://www.youtube.com/embed/\(videoID)"
        if let url = URL(string: embedURLString) {
            let request = URLRequest(url: url)
            nsView.load(request)
        }
    }
}

#Preview {
    VideoWebView(videoID: "samepleId")
}
