//
//  TextChunker.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 03/04/25.
//

import Foundation

/// Represents a single chunk of text with metadata about its position in the overall chunking process
struct TextChunk {
    /// The actual text content of this chunk
    let content: String
    /// Zero-based index of this chunk in the sequence
    let index: Int
    /// Total number of chunks in the complete sequence
    let totalChunks: Int
}

/// A utility class for intelligently splitting large text into smaller, manageable chunks
/// while preserving context and readability. Designed for AI processing and memory management.
///
/// The chunker uses a hierarchical approach:
/// 1. First attempts to split by sentences
/// 2. Falls back to word boundaries for long sentences
/// 3. Uses character splitting as a last resort
///
/// Thread-safe singleton implementation.
class TextChunker {
    /// Shared singleton instance
    static let shared = TextChunker()

    private init() {}

    /// Intelligently chunks text based on sentences and word boundaries while preserving context
    ///
    /// This method prioritizes readability by attempting to split at natural language boundaries.
    /// It includes overlap between chunks to maintain context for AI processing.
    ///
    /// - Parameters:
    ///   - text: The input text to be chunked
    ///   - maxChunkSize: Maximum character count per chunk
    ///   - overlap: Number of characters to overlap between chunks (default: 100)
    /// - Returns: Array of TextChunk objects with content and metadata
    /// - Note: Overlap is automatically capped at half of maxChunkSize to prevent infinite loops
    /// - Complexity: O(n) where n is the length of input text
    func chunkText(_ text: String, maxChunkSize: Int, overlap: Int = 100) -> [TextChunk] {
        guard !text.isEmpty else { return [] }

        // Ensure overlap is not larger than maxChunkSize
        let safeOverlap = min(overlap, maxChunkSize / 2)

        // If text is smaller than chunk size, return as single chunk
        if text.count <= maxChunkSize {
            return [TextChunk(content: text, index: 0, totalChunks: 1)]
        }

        let sentences = splitIntoSentences(text)
        var chunks: [String] = []
        var currentChunk = ""

        for sentence in sentences {
            let trimmedSentence = sentence.trimmingCharacters(in: .whitespacesAndNewlines)
            guard !trimmedSentence.isEmpty else { continue }

            // If single sentence is longer than maxChunkSize, split it by words
            if trimmedSentence.count > maxChunkSize {
                // Save current chunk if it exists
                if !currentChunk.isEmpty {
                    chunks.append(currentChunk.trimmingCharacters(in: .whitespacesAndNewlines))
                    currentChunk = ""
                }

                // Split the long sentence by words
                let wordChunks = chunkByWords(trimmedSentence, maxChunkSize: maxChunkSize)
                chunks.append(contentsOf: wordChunks)
                continue
            }

            // Check if adding this sentence would exceed the limit
            let testChunk = currentChunk.isEmpty ? trimmedSentence : currentChunk + " " + trimmedSentence

            if testChunk.count > maxChunkSize {
                if !currentChunk.isEmpty {
                    chunks.append(currentChunk.trimmingCharacters(in: .whitespacesAndNewlines))

                    // Add overlap from the end of current chunk
                    let overlapText = getOverlapText(from: currentChunk, maxLength: safeOverlap)
                    currentChunk = overlapText.isEmpty ? trimmedSentence : overlapText + " " + trimmedSentence

                    // Ensure the new chunk with overlap doesn't exceed maxChunkSize
                    if currentChunk.count > maxChunkSize {
                        currentChunk = trimmedSentence
                    }
                } else {
                    currentChunk = trimmedSentence
                }
            } else {
                currentChunk = testChunk
            }
        }

        // Add the last chunk if it's not empty
        if !currentChunk.isEmpty {
            let finalChunk = currentChunk.trimmingCharacters(in: .whitespacesAndNewlines)
            // Ensure final chunk doesn't exceed maxChunkSize
            if finalChunk.count > maxChunkSize {
                let wordChunks = chunkByWords(finalChunk, maxChunkSize: maxChunkSize)
                chunks.append(contentsOf: wordChunks)
            } else {
                chunks.append(finalChunk)
            }
        }

        // Validate all chunks before returning
        let validatedChunks = validateChunks(chunks, maxSize: maxChunkSize)

        // Convert to TextChunk objects
        return validatedChunks.enumerated().map { index, content in
            TextChunk(content: content, index: index, totalChunks: validatedChunks.count)
        }
    }

    /// Splits text into sentences using common sentence delimiters
    ///
    /// Recognizes periods, exclamation marks, and question marks as sentence boundaries.
    /// Handles edge cases where delimiters appear within sentences.
    ///
    /// - Parameter text: The input text to split into sentences
    /// - Returns: Array of sentence strings, including the delimiter
    /// - Note: Empty sentences are filtered out during processing
    private func splitIntoSentences(_ text: String) -> [String] {
        let sentenceDelimiters = CharacterSet(charactersIn: ".!?")
        var sentences: [String] = []
        var currentSentence = ""
        
        for character in text {
            currentSentence.append(character)
            
            if sentenceDelimiters.contains(character.unicodeScalars.first!) {
                sentences.append(currentSentence)
                currentSentence = ""
            }
        }
        
        // Add remaining text as a sentence
        if !currentSentence.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            sentences.append(currentSentence)
        }
        
        return sentences
    }

    /// Chunks text by word boundaries when sentences exceed the maximum chunk size
    ///
    /// This is a fallback method used when individual sentences are too long to fit
    /// within the specified chunk size. It attempts to maintain word integrity.
    ///
    /// - Parameters:
    ///   - text: The text to be chunked by words
    ///   - maxChunkSize: Maximum character count per chunk
    /// - Returns: Array of text chunks split at word boundaries
    /// - Note: If individual words exceed maxChunkSize, they are further split by characters
    private func chunkByWords(_ text: String, maxChunkSize: Int) -> [String] {
        let words = text.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }
        var chunks: [String] = []
        var currentChunk = ""

        for word in words {
            // If a single word is longer than maxChunkSize, split it by characters
            if word.count > maxChunkSize {
                // Save current chunk if it exists
                if !currentChunk.isEmpty {
                    chunks.append(currentChunk)
                    currentChunk = ""
                }

                // Split the long word into character chunks
                let characterChunks = chunkByCharacters(word, maxChunkSize: maxChunkSize)
                chunks.append(contentsOf: characterChunks)
                continue
            }

            let testChunk = currentChunk.isEmpty ? word : currentChunk + " " + word

            if testChunk.count <= maxChunkSize {
                currentChunk = testChunk
            } else {
                if !currentChunk.isEmpty {
                    chunks.append(currentChunk)
                }
                currentChunk = word
            }
        }

        if !currentChunk.isEmpty {
            chunks.append(currentChunk)
        }

        return chunks
    }

    /// Splits extremely long words by individual characters as a last resort
    ///
    /// This method is used when individual words exceed the maximum chunk size.
    /// It ensures that no chunk exceeds the specified limit, even if it means
    /// breaking words mid-character.
    ///
    /// - Parameters:
    ///   - text: The text (usually a single long word) to split
    ///   - maxChunkSize: Maximum character count per chunk
    /// - Returns: Array of character-based chunks
    /// - Warning: This may break words in unnatural ways and should only be used as a fallback
    private func chunkByCharacters(_ text: String, maxChunkSize: Int) -> [String] {
        var chunks: [String] = []
        var currentChunk = ""

        for character in text {
            if currentChunk.count >= maxChunkSize {
                chunks.append(currentChunk)
                currentChunk = String(character)
            } else {
                currentChunk.append(character)
            }
        }

        if !currentChunk.isEmpty {
            chunks.append(currentChunk)
        }

        return chunks
    }

    /// Validates that all chunks are within the maximum size limit and fixes any oversized chunks
    ///
    /// This is a safety mechanism to ensure no chunk exceeds the specified maximum size.
    /// If any chunk is found to be oversized, it is force-split using character boundaries.
    ///
    /// - Parameters:
    ///   - chunks: Array of text chunks to validate
    ///   - maxSize: Maximum allowed character count per chunk
    /// - Returns: Array of validated chunks, all within the size limit
    /// - Note: Logs warnings when force-splitting is required
    private func validateChunks(_ chunks: [String], maxSize: Int) -> [String] {
        var validatedChunks: [String] = []

        for chunk in chunks {
            if chunk.count <= maxSize {
                validatedChunks.append(chunk)
            } else {
                // If a chunk is still too large, force split it
                print("⚠️ Warning: Chunk of size \(chunk.count) exceeds max size \(maxSize), force splitting...")
                let forceSplitChunks = chunkByCharacters(chunk, maxChunkSize: maxSize)
                validatedChunks.append(contentsOf: forceSplitChunks)
            }
        }

        return validatedChunks
    }

    /// Extracts overlap text from the end of a chunk to maintain context between chunks
    ///
    /// This method attempts to create meaningful overlap by starting from word boundaries
    /// when possible, rather than cutting off mid-word.
    ///
    /// - Parameters:
    ///   - text: The source text to extract overlap from
    ///   - maxLength: Maximum number of characters to include in overlap
    /// - Returns: Overlap text, preferably starting at a word boundary
    /// - Note: If no word boundary is found, returns the raw character substring
    private func getOverlapText(from text: String, maxLength: Int) -> String {
        guard text.count > maxLength else { return text }
        
        let startIndex = text.index(text.endIndex, offsetBy: -maxLength)
        let overlapText = String(text[startIndex...])
        
        // Try to start from a word boundary
        if let spaceIndex = overlapText.firstIndex(of: " ") {
            return String(overlapText[spaceIndex...]).trimmingCharacters(in: .whitespacesAndNewlines)
        }
        
        return overlapText
    }

    /// Estimates the token count for a given text using a rough approximation
    ///
    /// Uses the common heuristic that 1 token ≈ 4 characters for English text.
    /// This is used for AI model token limit calculations.
    ///
    /// - Parameter text: The text to estimate tokens for
    /// - Returns: Estimated number of tokens
    /// - Note: This is an approximation; actual token counts may vary based on the tokenizer used
    func estimateTokenCount(_ text: String) -> Int {
        return text.count / 4
    }

    /// Determines if text needs to be chunked based on estimated token count
    ///
    /// Uses a conservative threshold of 75% of the maximum token limit to account
    /// for prompt overhead and response generation space.
    ///
    /// - Parameters:
    ///   - text: The text to evaluate
    ///   - maxTokens: Maximum token limit for the AI model
    /// - Returns: True if the text should be chunked, false otherwise
    /// - Note: Uses 75% threshold to leave room for system prompts and responses
    func needsChunking(_ text: String, maxTokens: Int) -> Bool {
        let estimatedTokens = estimateTokenCount(text)
        return estimatedTokens > (maxTokens * 3 / 4) // Use 75% of max tokens as threshold
    }
}

// MARK: - Chunking Strategies
extension TextChunker {

    /// Chunks text specifically optimized for AI prompt processing with context preservation
    ///
    /// This method is designed for AI workflows where the text needs to be processed
    /// in chunks while maintaining context. It reserves space for the base prompt
    /// and expected response, and adds chunk metadata for processing tracking.
    ///
    /// - Parameters:
    ///   - text: The content text to be chunked
    ///   - systemSpecs: System specifications containing memory and token limits
    ///   - basePrompt: The base prompt that will be prepended to each chunk (default: "")
    /// - Returns: Array of complete prompts ready for AI processing
    /// - Note: Reserves 200 characters for response space and adds chunk progress indicators
    func chunkForAIPrompt(_ text: String, systemSpecs: SystemSpecs, basePrompt: String = "") -> [String] {
        let availableSpace = systemSpecs.maxChunkSize - basePrompt.count - 200 // Reserve space for prompt and response
        let chunks = chunkText(text, maxChunkSize: availableSpace, overlap: 150)
        
        return chunks.map { chunk in
            let contextInfo = chunk.totalChunks > 1 ? 
                "\n[Processing chunk \(chunk.index + 1) of \(chunk.totalChunks)]\n" : ""
            return basePrompt + contextInfo + chunk.content
        }
    }

    /// Intelligently combines multiple AI responses from chunked processing
    ///
    /// Takes an array of responses from processing individual chunks and combines them
    /// into a coherent final response. Handles both single and multiple chunk scenarios.
    ///
    /// - Parameters:
    ///   - responses: Array of AI responses from individual chunks
    ///   - summaryPrompt: Optional prompt for final summarization (currently unused)
    /// - Returns: Combined response text with part indicators for multiple chunks
    /// - Note: For multiple chunks, each response is labeled with its part number
    /// - Future Enhancement: Could integrate with AI for intelligent summarization
    func combineChunkedResponses(_ responses: [String], summaryPrompt: String? = nil) -> String {
        guard !responses.isEmpty else { return "" }
        
        if responses.count == 1 {
            return responses.first!
        }
        
        // If we have multiple responses, combine them
        let combined = responses.enumerated().map { index, response in
            "Part \(index + 1): \(response)"
        }.joined(separator: "\n\n")
        
        // If a summary prompt is provided, this would be where you'd
        // send the combined text for final summarization
        return combined
    }
}
