//
//  SafeMemoryTest.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 03/04/25.
//

import Foundation

class SafeMemoryTest {
    
    /// Safe test that won't cause assertion failures
    static func runSafeTests() {
        print("🧪 Running Safe Memory Management Tests...")
        print("=" * 50)
        
        testSystemDetection()
        testChunkingWithValidation()
        testMemoryAwareConfiguration()
        
        print("\n✅ All safe tests completed successfully!")
    }
    
    static func testSystemDetection() {
        print("\n📊 System Detection Test:")
        print("-" * 30)
        
        let systemSpecs = SystemSpecsManager.shared.getSystemSpecs()
        let pressure = SystemSpecsManager.shared.getMemoryPressureLevel()
        
        print("✅ Total Memory: \(String(format: "%.1f", systemSpecs.totalMemoryGB)) GB")
        print("✅ Available Memory: \(String(format: "%.1f", systemSpecs.availableMemoryGB)) GB")
        print("✅ Recommended Tokens: \(systemSpecs.recommendedTokenCount)")
        print("✅ Memory Pressure: \(pressure.description)")
        print("✅ Should Chunk: \(systemSpecs.shouldUseChunking)")
        print("✅ Max Chunk Size: \(systemSpecs.maxChunkSize)")
        
        // Validate reasonable values without assertions
        if systemSpecs.totalMemoryGB > 0 && systemSpecs.availableMemoryGB > 0 {
            print("✅ Memory detection working correctly")
        } else {
            print("⚠️ Memory detection may have issues")
        }
    }
    
    static func testChunkingWithValidation() {
        print("\n📝 Safe Chunking Test:")
        print("-" * 30)
        
        let testTexts = [
            "Short text",
            "This is a medium length text that should be handled properly by the chunking system.",
            """
            This is a longer text that will definitely need to be chunked when using small chunk sizes. 
            It contains multiple sentences and should demonstrate how the chunking algorithm preserves 
            sentence boundaries while maintaining reasonable chunk sizes. The system should handle this 
            gracefully without any assertion failures.
            """
        ]
        
        let chunker = TextChunker.shared
        
        for (index, text) in testTexts.enumerated() {
            print("\n   Test \(index + 1): \(text.count) characters")
            
            // Test with reasonable chunk sizes
            let chunkSizes = [100, 200, 500]
            
            for chunkSize in chunkSizes {
                let chunks = chunker.chunkText(text, maxChunkSize: chunkSize, overlap: 20)
                
                var maxChunkSize = 0
                var allValid = true
                
                for chunk in chunks {
                    maxChunkSize = max(maxChunkSize, chunk.content.count)
                    if chunk.content.count > chunkSize * 2 { // Allow some flexibility
                        allValid = false
                    }
                }
                
                let status = allValid ? "✅" : "⚠️"
                print("     \(status) Chunk size \(chunkSize): \(chunks.count) chunks, max size: \(maxChunkSize)")
            }
        }
        
        print("✅ Chunking validation completed")
    }
    
    static func testMemoryAwareConfiguration() {
        print("\n⚙️ Memory-Aware Configuration Test:")
        print("-" * 30)
        
        let aiService = LocalAIService.shared
        let memoryInfo = aiService.getMemoryInfo()
        
        print("✅ AI Service initialized")
        print("✅ Available Memory: \(String(format: "%.1f", memoryInfo.available)) GB")
        print("✅ Memory Pressure: \(memoryInfo.pressure.description)")
        
        // Test chunking decision logic
        let chunker = TextChunker.shared
        let systemSpecs = SystemSpecsManager.shared.getSystemSpecs()
        
        let testPrompt = String(repeating: "This is a test sentence. ", count: 100)
        let needsChunking = chunker.needsChunking(testPrompt, maxTokens: systemSpecs.recommendedTokenCount)
        
        print("✅ Test prompt length: \(testPrompt.count) characters")
        print("✅ Estimated tokens: \(chunker.estimateTokenCount(testPrompt))")
        print("✅ Needs chunking: \(needsChunking)")
        
        if needsChunking {
            let chunks = chunker.chunkForAIPrompt(testPrompt, systemSpecs: systemSpecs)
            print("✅ Would create \(chunks.count) chunks")
        }
        
        print("✅ Memory-aware configuration working")
    }
    
    /// Test specific edge cases safely
    static func testEdgeCasesSafely() {
        print("\n🔍 Edge Cases Test:")
        print("-" * 30)
        
        let chunker = TextChunker.shared
        
        // Test 1: Empty string
        let emptyResult = chunker.chunkText("", maxChunkSize: 100)
        print("✅ Empty string: \(emptyResult.count) chunks")
        
        // Test 2: Very short text
        let shortResult = chunker.chunkText("Hi", maxChunkSize: 100)
        print("✅ Short text: \(shortResult.count) chunks")
        
        // Test 3: Text exactly at limit
        let exactText = String(repeating: "a", count: 100)
        let exactResult = chunker.chunkText(exactText, maxChunkSize: 100)
        print("✅ Exact limit text: \(exactResult.count) chunks")
        
        // Test 4: Very long word (but handle gracefully)
        let longWord = String(repeating: "supercalifragilisticexpialidocious", count: 10)
        let longWordResult = chunker.chunkText(longWord, maxChunkSize: 50)
        print("✅ Long word: \(longWordResult.count) chunks")
        
        print("✅ Edge cases handled safely")
    }
    
    /// Manual test you can call from anywhere
    static func quickTest() {
        print("🚀 Quick Memory Management Test")
        
        let specs = SystemSpecsManager.shared.getSystemSpecs()
        print("Memory: \(String(format: "%.1f", specs.availableMemoryGB))GB, Tokens: \(specs.recommendedTokenCount)")
        
        let testText = "This is a test of the chunking system with a reasonable amount of text."
        let chunks = TextChunker.shared.chunkText(testText, maxChunkSize: 30)
        print("Text chunked into \(chunks.count) pieces")
        
        print("✅ Quick test completed")
    }
}

// String extension for formatting
extension String {
    static func *(lhs: String, rhs: Int) -> String {
        return String(repeating: lhs, count: rhs)
    }
}
