//
//  SystemSpecsManager.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 03/04/25.
//

import Foundation
import Darwin

struct SystemSpecs {
    let totalMemoryGB: Double
    let availableMemoryGB: Double
    let recommendedTokenCount: Int
    let shouldUseChunking: Bool
    let maxChunkSize: Int
}

class SystemSpecsManager {
    static let shared = SystemSpecsManager()
    
    private init() {}
    
    /// Get current system specifications and memory recommendations
    func getSystemSpecs() -> SystemSpecs {
        let totalMemory = getTotalMemoryGB()
        let availableMemory = getAvailableMemoryGB()
        
        // Determine optimal token count based on available memory
        let tokenCount = calculateOptimalTokenCount(availableMemoryGB: availableMemory)
        let shouldChunk = availableMemory < 8.0 // Chunk if less than 8GB available
        let chunkSize = calculateChunkSize(availableMemoryGB: availableMemory)
        
        return SystemSpecs(
            totalMemoryGB: totalMemory,
            availableMemoryGB: availableMemory,
            recommendedTokenCount: tokenCount,
            shouldUseChunking: shouldChunk,
            maxChunkSize: chunkSize
        )
    }
    
    /// Get total system memory in GB
    private func getTotalMemoryGB() -> Double {
        var size = MemoryLayout<UInt64>.size
        var totalMemory: UInt64 = 0
        
        if sysctlbyname("hw.memsize", &totalMemory, &size, nil, 0) == 0 {
            return Double(totalMemory) / (1024 * 1024 * 1024) // Convert to GB
        }
        
        return 8.0 // Default fallback
    }
    
    /// Get available system memory in GB using vm_statistics64
    private func getAvailableMemoryGB() -> Double {
        var vmStats = vm_statistics64()
        var count = mach_msg_type_number_t(MemoryLayout<vm_statistics64>.size / MemoryLayout<integer_t>.size)
        
        let result = withUnsafeMutablePointer(to: &vmStats) {
            $0.withMemoryRebound(to: integer_t.self, capacity: Int(count)) {
                host_statistics64(mach_host_self(), HOST_VM_INFO64, $0, &count)
            }
        }
        
        guard result == KERN_SUCCESS else {
            return getTotalMemoryGB() * 0.5 // Fallback to 50% of total
        }
        
        let pageSize = vm_kernel_page_size
        let freeMemory = UInt64(vmStats.free_count) * UInt64(pageSize)
        let inactiveMemory = UInt64(vmStats.inactive_count) * UInt64(pageSize)
        let availableMemory = freeMemory + inactiveMemory
        
        return Double(availableMemory) / (1024 * 1024 * 1024) // Convert to GB
    }
    
    /// Calculate optimal token count based on available memory
    private func calculateOptimalTokenCount(availableMemoryGB: Double) -> Int {
        switch availableMemoryGB {
        case 16...:
            return 32768  // High-end systems
        case 12..<16:
            return 24576  // Upper mid-range
        case 8..<12:
            return 16384  // Mid-range
        case 4..<8:
            return 8192   // Lower mid-range
        case 2..<4:
            return 4096   // Low-end
        default:
            return 2048   // Very low memory
        }
    }
    
    /// Calculate chunk size for text processing
    private func calculateChunkSize(availableMemoryGB: Double) -> Int {
        switch availableMemoryGB {
        case 8...:
            return 4000   // Larger chunks for better systems
        case 4..<8:
            return 2000   // Medium chunks
        case 2..<4:
            return 1000   // Smaller chunks
        default:
            return 500    // Very small chunks for low memory
        }
    }
    
    /// Monitor memory usage and suggest adjustments
    func getMemoryPressureLevel() -> MemoryPressureLevel {
        let availableMemory = getAvailableMemoryGB()
        let totalMemory = getTotalMemoryGB()
        let usagePercentage = (totalMemory - availableMemory) / totalMemory
        
        switch usagePercentage {
        case 0..<0.6:
            return .low
        case 0.6..<0.8:
            return .moderate
        case 0.8..<0.9:
            return .high
        default:
            return .critical
        }
    }
}

enum MemoryPressureLevel {
    case low, moderate, high, critical
    
    var description: String {
        switch self {
        case .low: return "Low memory pressure"
        case .moderate: return "Moderate memory pressure"
        case .high: return "High memory pressure - consider reducing token count"
        case .critical: return "Critical memory pressure - chunking recommended"
        }
    }
}
