//
//  StartupManager.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 30/05/25.
//

import Foundation
import LLM

struct ModelInfo {
    let urlSlug: String
    let description: String
    let sizeInGB: Float
}

class StartupManager: ObservableObject {
    @Published var isModelReady = false
    @Published var isDownloading = false
    @Published var downloadProgress: Double = 0.0

    let aiService = LocalAIService.shared
    private let systemSpecsManager = SystemSpecsManager.shared
    
    let modelFileMap: [String: String] = [
           "Gemma": "gemma-2-2b-it-Q4_K_M.gguf",
          // "Qwen": "Qwen3-4B-Q4_K_M.gguf"
    ]
    
    let repositoryMap: [String: ModelInfo] = [
        "Gemma": ModelInfo(
            urlSlug: "bartowski/gemma-2-2b-it-GGUF",
            description: "Lightweight model, fast for general-purpose tasks",
            sizeInGB:1.7
        ),
//        "Qwen": ModelInfo(
//            urlSlug: "unsloth/Qwen3-4B-GGUF",
//            description: "Larger model optimized for complex AI reasoning",
//            sizeInGB: 2.08
//        )
    ]
    
    
    func checkModelExist(named userModelName: String) -> Bool {
        guard let expectedFileName = modelFileMap[userModelName] else {
            return false
        }
        
        let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        do {
            let contents = try FileManager.default.contentsOfDirectory(at: documentsURL, includingPropertiesForKeys: nil)
            for file in contents {
                if file.lastPathComponent == expectedFileName {
                    return true
                }
            }
        } catch {
            print("❌ Failed to read documents directory: \(error)")
        }
        
        return false
    }

    
    
    func checkModelAvailability() {
        if aiService.model != nil {
            isModelReady = true
        }
    }
    
    func startDownload(models: [String]) {
        isDownloading = true
        downloadProgress = 0.0
 

        Task {
            var completed = 0
            let total = models.count

            for name in models {
                guard let repo = repositoryMap[name] else {
                    print("❌ Unknown model name: \(name)")
                    continue
                }

                print("⬇️ Starting download for \(name) from \(repo)")

                let huggingFaceModel = HuggingFaceModel(repo.urlSlug, template: .gemma)

                do {
                    // Get memory-aware token count
                    let systemSpecs = systemSpecsManager.getSystemSpecs()
                    print("🖥️ Using \(systemSpecs.recommendedTokenCount) tokens for model download")

                    let model = try await LLM(from: huggingFaceModel, maxTokenCount: Int32(systemSpecs.recommendedTokenCount)) { [weak self] progress in
                        DispatchQueue.main.async {
                            // Scale progress per model
                            let modelProgress = Double(completed) / Double(total)
                            let scaledProgress = modelProgress + (progress / Double(total))
                            self?.downloadProgress = scaledProgress
                        }
                    }

                    if let model {
                        await MainActor.run {
                            aiService.model = model // Optional: Replace this logic if you want to store multiple
                            print("✅ Finished downloading \(name)")
                        }
                    }

                    completed += 1

                } catch {
                    print("❌ Failed to download \(name): \(error)")
                }
            }

            await MainActor.run {
                self.isDownloading = false
                self.isModelReady = aiService.model != nil
            }
        }
    }
    

}

