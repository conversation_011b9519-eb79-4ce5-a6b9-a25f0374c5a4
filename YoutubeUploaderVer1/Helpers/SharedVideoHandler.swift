//
//  SharedVideoHandler.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 22/05/25.
//

import Foundation

class SharedVideoHandler: ObservableObject {
    @Published var sharedVideoURL: URL?
    @Published var sharedVideoTitle: String?
    
    private let sharedDefaults: UserDefaults?
    private let queue = DispatchQueue(label: "com.codecraft.sharedvideohandler")
    
    init() {
        // Initialize with the app group identifier
        if let _ = Bundle.main.bundleIdentifier {
            sharedDefaults = UserDefaults(suiteName: Constants.appGroupIdentifier)
        } else {
            sharedDefaults = nil
        }
        
        // Check for shared video on initialization
        checkForSharedVideo()
        
        // Set up observation for changes
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(userDefaultsDidChange),
            name: UserDefaults.didChangeNotification,
            object: sharedDefaults
        )
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    @objc private func userDefaultsDidChange() {
        checkForSharedVideo()
    }
    
    func checkForSharedVideo() {
        queue.async { [weak self] in
            guard let self = self else { return }
                        
            
            if let urlString = self.sharedDefaults?.string(forKey: Constants.sharedVideoURLKey) {
                
                if let url = URL(string: urlString) {
                    let title = self.sharedDefaults?.string(forKey: Constants.sharedVideoTitleKey)
                    
                    DispatchQueue.main.async {
                        self.sharedVideoURL = url
                        self.sharedVideoTitle = title
                    }
                    
                    // Clear the shared data after reading
//                    self.clearSharedData()
                } else {
                    print("❌ SharedVideoHandler: Invalid URL string: \(urlString)")
                }
            } else {
                print("📱 SharedVideoHandler: No shared video URL found")
            }
        }
    }
    
    private func clearSharedData() {
        queue.async { [weak self] in
            self?.sharedDefaults?.removeObject(forKey: Constants.sharedVideoURLKey)
            self?.sharedDefaults?.removeObject(forKey: Constants.sharedVideoTitleKey)
            self?.sharedDefaults?.synchronize()
        }
    }
}
