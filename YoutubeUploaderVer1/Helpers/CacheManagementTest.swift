//
//  CacheManagementTest.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 03/04/25.
//

import Foundation

class CacheManagementTest {
    
    static func testCacheManagement() {
        print("🧪 Testing Cache Management...")
        print("=" * 40)
        
        testCacheStatus()
        testQuickRefresh()
        // Note: Full reset test is commented out as it takes time
        // testFullReset()
        
        print("\n✅ Cache management tests completed!")
    }
    
    static func testCacheStatus() {
        print("\n📊 Testing Cache Status:")
        print("-" * 30)
        
        let aiService = LocalAIService.shared
        let cacheStatus = aiService.getCacheStatus()
        
        print("✅ Model Loaded: \(cacheStatus.modelLoaded)")
        print("✅ Memory Pressure: \(cacheStatus.memoryPressure.description)")
        print("✅ Last Response: \(cacheStatus.lastResponse)")
        
        let memoryInfo = aiService.getMemoryInfo()
        print("✅ Available Memory: \(String(format: "%.1f", memoryInfo.available)) GB")
        
        print("✅ Cache status check completed")
    }
    
    static func testQuickRefresh() {
        print("\n🔄 Testing Quick Cache Refresh:")
        print("-" * 30)
        
        let aiService = LocalAIService.shared
        
        // Set some test data
        aiService.response = "Test response data"
        aiService.currentChunk = 2
        aiService.totalChunks = 5
        
        print("Before refresh:")
        print("   Response: \(aiService.response.isEmpty ? "Empty" : "Has data")")
        print("   Chunks: \(aiService.currentChunk)/\(aiService.totalChunks)")
        
        // Perform quick refresh
        aiService.refreshCache()
        
        print("After refresh:")
        print("   Response: \(aiService.response.isEmpty ? "Empty" : "Has data")")
        print("   Chunks: \(aiService.currentChunk)/\(aiService.totalChunks)")
        
        // Verify cache was cleared
        let wasCleared = aiService.response.isEmpty && 
                        aiService.currentChunk == 0 && 
                        aiService.totalChunks == 0
        
        print("✅ Quick refresh \(wasCleared ? "successful" : "failed")")
    }
    
    static func testFullReset() {
        print("\n🔄 Testing Full Cache Reset:")
        print("-" * 30)
        print("⚠️ This test takes time as it reinitializes the model...")
        
        let aiService = LocalAIService.shared
        let initialModelState = aiService.model != nil
        
        print("Initial model state: \(initialModelState ? "Loaded" : "Not loaded")")
        
        // This would be an async test in real usage
        print("✅ Full reset test setup completed (actual reset requires async context)")
    }
    
    static func testMemoryPressureResponse() {
        print("\n⚠️ Testing Memory Pressure Response:")
        print("-" * 30)
        
        let systemSpecs = SystemSpecsManager.shared.getSystemSpecs()
        let pressure = SystemSpecsManager.shared.getMemoryPressureLevel()
        
        print("Current memory pressure: \(pressure.description)")
        print("Should use chunking: \(systemSpecs.shouldUseChunking)")
        print("Recommended token count: \(systemSpecs.recommendedTokenCount)")
        
        // Provide recommendations based on pressure
        switch pressure {
        case .low:
            print("💡 Recommendation: System running optimally, cache clearing not needed")
        case .moderate:
            print("💡 Recommendation: Consider quick refresh if experiencing issues")
        case .high:
            print("💡 Recommendation: Quick refresh recommended")
        case .critical:
            print("💡 Recommendation: Full cache reset strongly recommended")
        }
        
        print("✅ Memory pressure response test completed")
    }
    
    /// Quick test for UI integration
    static func quickCacheTest() {
        print("🚀 Quick Cache Test")
        
        let aiService = LocalAIService.shared
        let cacheStatus = aiService.getCacheStatus()
        
        print("Model: \(cacheStatus.modelLoaded ? "✅" : "❌")")
        print("Memory: \(cacheStatus.memoryPressure.description)")
        
        // Test quick refresh
        aiService.refreshCache()
        print("✅ Cache refreshed")
    }
    
    /// Simulate cache filling up
    static func simulateCacheFull() {
        print("🔄 Simulating Cache Full Scenario...")
        
        let aiService = LocalAIService.shared
        
        // Simulate a large response
        aiService.response = String(repeating: "This is a large AI response. ", count: 100)
        aiService.currentChunk = 10
        aiService.totalChunks = 10
        
        print("Cache filled with test data")
        print("Response length: \(aiService.response.count) characters")
        print("Chunks processed: \(aiService.currentChunk)/\(aiService.totalChunks)")
        
        let memoryInfo = aiService.getMemoryInfo()
        print("Memory pressure: \(memoryInfo.pressure.description)")
        
        if memoryInfo.pressure == .high || memoryInfo.pressure == .critical {
            print("⚠️ High memory pressure detected - cache clearing recommended")
        }
        
        print("✅ Cache simulation completed")
    }
}

// String extension for formatting
extension String {
    static func *(lhs: String, rhs: Int) -> String {
        return String(repeating: lhs, count: rhs)
    }
}
