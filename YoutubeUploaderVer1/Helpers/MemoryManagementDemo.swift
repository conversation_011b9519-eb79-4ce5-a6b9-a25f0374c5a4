//
//  MemoryManagementDemo.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 03/04/25.
//

import Foundation

class MemoryManagementDemo {
    static func runDemo() {
        print("🚀 Memory Management Demo")
        print("=" * 50)
        
        demonstrateSystemDetection()
        demonstrateChunking()
        demonstrateMemoryAwareAI()
        
        print("\n✅ Demo completed!")
    }
    
    static func demonstrateSystemDetection() {
        print("\n📊 System Detection Demo:")
        print("-" * 30)
        
        let systemSpecs = SystemSpecsManager.shared.getSystemSpecs()
        let pressure = SystemSpecsManager.shared.getMemoryPressureLevel()
        
        print("🖥️  System Specifications:")
        print("   Total Memory: \(String(format: "%.1f", systemSpecs.totalMemoryGB)) GB")
        print("   Available Memory: \(String(format: "%.1f", systemSpecs.availableMemoryGB)) GB")
        print("   Memory Pressure: \(pressure.description)")
        print("")
        print("⚙️  AI Configuration:")
        print("   Recommended Token Count: \(systemSpecs.recommendedTokenCount)")
        print("   Should Use Chunking: \(systemSpecs.shouldUseChunking ? "Yes" : "No")")
        print("   Max Chunk Size: \(systemSpecs.maxChunkSize) characters")
        
        // Show what this means for the user
        let memoryCategory = getMemoryCategory(systemSpecs.availableMemoryGB)
        print("")
        print("📈 Performance Expectation:")
        print("   Category: \(memoryCategory.name)")
        print("   Expected Performance: \(memoryCategory.description)")
    }
    
    static func demonstrateChunking() {
        print("\n📝 Text Chunking Demo:")
        print("-" * 30)
        
        let sampleTranscript = """
        Welcome to this comprehensive tutorial on iOS development. In this video, we'll explore the fundamentals of SwiftUI and how to build modern, responsive user interfaces. We'll start with basic concepts like views and modifiers, then move on to more advanced topics such as state management, data binding, and navigation. Throughout this tutorial, you'll learn best practices for organizing your code, handling user input, and creating smooth animations. We'll also cover important topics like accessibility, performance optimization, and testing your SwiftUI applications. By the end of this video, you'll have a solid understanding of how to create professional-quality iOS apps using SwiftUI. Don't forget to like and subscribe for more iOS development content!
        """
        
        let chunker = TextChunker.shared
        let systemSpecs = SystemSpecsManager.shared.getSystemSpecs()
        
        print("📄 Original Text:")
        print("   Length: \(sampleTranscript.count) characters")
        print("   Estimated Tokens: \(chunker.estimateTokenCount(sampleTranscript))")
        print("   Needs Chunking: \(chunker.needsChunking(sampleTranscript, maxTokens: systemSpecs.recommendedTokenCount) ? "Yes" : "No")")
        
        // Demonstrate chunking with different sizes
        let chunkSizes = [200, 500, 1000]
        
        for chunkSize in chunkSizes {
            let chunks = chunker.chunkText(sampleTranscript, maxChunkSize: chunkSize, overlap: 50)
            print("")
            print("🔪 Chunking with \(chunkSize) character limit:")
            print("   Number of chunks: \(chunks.count)")
            
            for (index, chunk) in chunks.enumerated() {
                let preview = chunk.content.prefix(50)
                print("   Chunk \(index + 1): \(chunk.content.count) chars - \"\(preview)...\"")
            }
        }
    }
    
    static func demonstrateMemoryAwareAI() {
        print("\n🤖 Memory-Aware AI Demo:")
        print("-" * 30)
        
        let aiService = LocalAIService.shared
        let memoryInfo = aiService.getMemoryInfo()
        
        print("🧠 AI Service Status:")
        print("   Model Loaded: \(aiService.model != nil ? "Yes" : "No")")
        print("   Available Memory: \(String(format: "%.1f", memoryInfo.available)) GB")
        print("   Memory Pressure: \(memoryInfo.pressure.description)")
        print("   Currently Processing: \(aiService.isProcessing ? "Yes" : "No")")
        
        if aiService.totalChunks > 1 {
            print("   Processing Chunks: \(aiService.currentChunk)/\(aiService.totalChunks)")
        }
        
        print("")
        print("💡 Recommendations:")
        
        switch memoryInfo.pressure {
        case .low:
            print("   ✅ System is running optimally")
            print("   ✅ Can handle large transcripts without chunking")
        case .moderate:
            print("   ⚠️  Consider closing other applications")
            print("   ✅ Chunking available if needed")
        case .high:
            print("   ⚠️  High memory usage detected")
            print("   🔄 Automatic chunking recommended")
        case .critical:
            print("   🚨 Critical memory pressure")
            print("   🔄 Aggressive chunking will be used")
            print("   💾 Consider restarting the application")
        }
    }
    
    // Helper function to categorize memory
    static func getMemoryCategory(_ availableGB: Double) -> (name: String, description: String) {
        switch availableGB {
        case 16...:
            return ("High-End", "Excellent performance, no chunking needed")
        case 8..<16:
            return ("Mid-Range", "Good performance, minimal chunking")
        case 4..<8:
            return ("Standard", "Adequate performance, chunking for large texts")
        case 2..<4:
            return ("Low-End", "Basic performance, frequent chunking")
        default:
            return ("Minimal", "Limited performance, aggressive chunking")
        }
    }
}

// String extension for demo formatting
extension String {
    static func *(lhs: String, rhs: Int) -> String {
        return String(repeating: lhs, count: rhs)
    }
}
