//
//  Colors.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 10/04/25.
//

import Foundation
import SwiftUI


enum AppColor: String {
    // Legacy colors (keeping for compatibility)
    case youtubeRed = "YoutubeRed"
    case darkGrayBackground = "DarkGrayBackground"
    case darkBackground = "DarkBackground"
    case grayText = "GrayText"
    case primary = "MainColor"
    case iconColor = "IconColor"
    case blackColor = "darkBlackColor"

    // New modern color palette
    case surfacePrimary = "SurfacePrimary"
    case surfaceSecondary = "SurfaceSecondary"
    case surfaceTertiary = "SurfaceTertiary"
    case accentBlue = "AccentBlue"
    case accentGreen = "AccentGreen"
    case accentOrange = "AccentOrange"
    case accentPurple = "AccentPurple"
    case textPrimary = "TextPrimary"
    case textSecondary = "TextSecondary"
    case textTertiary = "TextTertiary"
    case borderPrimary = "BorderPrimary"
    case borderSecondary = "BorderSecondary"
    case successGreen = "SuccessGreen"
    case warningOrange = "WarningOrange"
    case errorRed = "ErrorRed"
    case backgroundGradientStart = "BackgroundGradientStart"
    case backgroundGradientEnd = "BackgroundGradientEnd"

    var color: Color {
        Color(self.rawValue)
    }
}

