//
//  Theme.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 18/04/25.
//


import Foundation
import SwiftUI


enum ThemePreference: String, CaseIterable, Identifiable {
    case system, light, dark
    var id: String { self.rawValue }

    // User-friendly description for UI
    var description: String {
        switch self {
        case .system: return "System"
        case .light: return "Light"
        case .dark: return "Dark"
        }
    }

    var colorScheme: ColorScheme? {
        switch self {
        case .system: return nil
        case .light: return .light
        case .dark: return .dark
        }
    }

    func next() -> ThemePreference {
        switch self {
        case .system: return .light
        case .light: return .dark
        case .dark: return .system
        }
    }
}


