//
//  AppFonts.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 11/04/25.
//

import Foundation
import SwiftUI

enum AppFontStyle {
    case largeTitle
    case title1
    case title2
    case title3
    case headline
    case body
    case callout
    case subheadline
    case footnote
    case caption1
    case caption2

    // The corresponding standard SwiftUI Font style (Supports Dynamic Type).
    var style: Font {
        switch self {
        case .largeTitle:   return .largeTitle // ~34pt
        case .title1:       return .title      // ~28pt
        case .title2:       return .title2     // ~22pt
        case .title3:       return .title3     // ~20pt
        case .headline:     return .headline   // ~17pt 
        case .body:         return .body       // ~17pt
        case .callout:      return .callout    // ~16pt
        case .subheadline:  return .subheadline // ~15pt
        case .footnote:     return .footnote   // ~13pt
        case .caption1:     return .caption    // ~12pt
        case .caption2:     return .caption2   // ~11pt
        }
    }

    // A fallback fixed CGFloat size (Does NOT support Dynamic Type well).
    
    var fixedSize: CGFloat {
        switch self {
        case .largeTitle:   return 34.0
        case .title1:       return 28.0
        case .title2:       return 22.0
        case .title3:       return 20.0
        case .headline:     return 17.0
        case .body:         return 17.0
        case .callout:      return 16.0
        case .subheadline:  return 15.0
        case .footnote:     return 13.0
        case .caption1:     return 12.0
        case .caption2:     return 11.0
        }
    }
}
