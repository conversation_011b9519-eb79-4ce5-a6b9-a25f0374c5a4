//
//  Texts.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 24/04/25.
//

import Foundation


enum TextConstants{
    enum Overview {
        static let creatorDashboard = "Creator Dashboard"
        static let welcomeBack = "Welcome back"
        static let yourChannelStats = "Your Channel Stats"
        static let totalViews = "Total Views"
        static let watchTime = "Watch Time"
        static let subscribers = "Subscribers"
        static let totalRevenue = "Total Revenue"
        static let sinceWhen = "Since last week"
        static let rupees = ""
        static let dollars = "Dollars"
        static let avgViewDuration = "Avg View Duration"
        static let minutes = "Minutes"
        static let views = "Views"
        static let topViewedVideo = "Top Trending Video"
        static let noTopVideo = "No top video found."
        static let noTopVideoDesc = "Looks like there's no data for this time period.\nStart uploading videos and come back soon!"
        static let channelAnalytics = "Analytics of your channel"
        static let loadingChart = "Loading Chart Data..."
       }
    
    enum Playlists{
        static let playlistsHeaderTitle = "Your Playlists"
        static let playlistsHeaderSubtitle = "Organize your content into collections"
        static let oneMonthAgo = "1 month ago"
        static let monthsAgo = "months ago"
        static let oneWeekAgo = "1 week ago"
        static let weeksAgo = "weeks ago"
        static let oneDayAgo = "1 day ago"
        static let daysAgo = "days ago"
        static let today = "Today"
        static let NoDesc = "There is no description for this item"
        enum PlaylistDetailView {
            static let backToPlaylistsButton = "Back to Playlists"
        }
        static let noPlaylistsTitle = "No Playlists Found"
        static let noPlaylistsDescription = "This account doesn't have any created playlists."
    }
    
    enum SignIn {
        static let title = "YouTube Uploader"
        static let subtitle = "Sign in to manage and upload your videos"
        static let googleButton = "Sign in with Google"
        static let termsAndPrivacy = "By signing in, you agree to YouTube's Terms of Service\nand acknowledge that you have read our Privacy Policy"
    }
    enum UserVideos {
        static let title = "Your Videos"
        static let subtitle = "Manage and analyze your video content"
        static let searchPlaceholder = "Search videos, playlists..."
        static let noVideosTitle = "No Videos Found"
        static let noVideosDescription = "This account doesn't have any uploaded videos."
        static let noVideosBySearchTitle = "No videos match your search."
        static let noVideosBySearchDesc = "Try adjusting your search terms."
    }
    enum UploadVideos{
        static let title = "Upload Videos"
        static let subtitle = "Upload videos from here"
    }
    
    enum ScriptWriter{
        static let title = "AI Script Writer"
        static let subtitle = "Generate scripts for your videos from AI"
    }
}
