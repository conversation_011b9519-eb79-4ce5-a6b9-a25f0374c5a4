//
//  Icons.swift
//  YoutubeUploaderVer1
//
//  Created by Shashank B on 03/06/25.
//

import Foundation

enum Platform: String, CaseIterable {
    case youtube = "YouTube"
    case instagram = "Instagram"
    case tiktok = "TikTok"
    
    var iconName: String {
        switch self {
        case .youtube:
            return "youtube_icon"
        case .instagram:
            return "instagram_icon"
        case .tiktok:
            return "tiktok_icon"
        }
    }
}
